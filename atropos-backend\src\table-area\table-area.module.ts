// src/table-area/table-area.module.ts
import { Module } from '@nestjs/common';
import { TableAreaService } from './table-area.service';
import { TableAreaController } from './table-area.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [TableAreaController],
  providers: [TableAreaService],
  exports: [TableAreaService], // <PERSON><PERSON><PERSON> modüllerde (örn: Table modülü) kullanmak istersen
})
export class TableAreaModule {}
