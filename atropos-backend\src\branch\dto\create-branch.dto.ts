// src/branch/dto/create-branch.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEmail,
  IsBoolean,
  IsNumber,
  Min,
  Max,
  IsInt,
  IsArray,
  ArrayMinSize,
  ArrayMaxSize,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateBranchDto {
  @IsString()
  @IsNotEmpty()
  companyId: string; // Hangi şirkete bağlı olduğu

  @IsString()
  @IsNotEmpty()
  code: string; // e.g. "IST01"

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  address: string;

  @IsString()
  @IsNotEmpty()
  phone: string;

  @IsString()
  @IsOptional()
  @IsEmail()
  email?: string;

  @IsNumber()
  @IsOptional()
  @Min(-90)
  @Max(90)
  latitude?: number;

  @IsNumber()
  @IsOptional()
  @Min(-180)
  @Max(180)
  longitude?: number;

  @IsString()
  @IsOptional()
  serverIp?: string;

  @IsInt()
  @IsOptional()
  @Min(1)
  @Max(65535)
  serverPort?: number;

  @IsBoolean()
  @IsOptional()
  isMainBranch?: boolean;

  @IsString()
  @IsOptional()
  openingTime?: string; // Format: "HH:MM"

  @IsString()
  @IsOptional()
  closingTime?: string; // Format: "HH:MM"

  @IsArray()
  @IsOptional()
  @IsInt({ each: true })
  @Min(1, { each: true })
  @Max(7, { each: true })
  @ArrayMinSize(1)
  @ArrayMaxSize(7)
  @Type(() => Number) // Gelen veriyi Number'a dönüştürmek için
  workingDays?: number[]; // 1=Monday, 7=Sunday

  @IsString()
  @IsOptional()
  cashRegisterId?: string; // ÖKC cihaz no

  @IsString()
  @IsOptional()
  posTerminalId?: string; // EFT-POS terminal ID

  @IsBoolean()
  @IsOptional()
  active?: boolean;
}
