// src/dashboard/MainDashboard.tsx
import React from 'react';
import {
  makeStyles,
  shorthands,
  tokens,
  Text,
  Toolbar,
  ToolbarButton,
  ToolbarDivider,
  Tab<PERSON>ist,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@fluentui/react-components';
import {
  HomeFilled,
  ListFilled,
  MoneyFilled,
  SettingsFilled,
  ArrowExitFilled,
  FoodFilled,
  PeopleFilled,
  GridFilled, // Yeni ikon
  BoxFilled, // Yeni ikon
  DataBarVerticalFilled, // Yeni ikon
} from '@fluentui/react-icons';

// Stil tanımlamaları
const useStyles = makeStyles({
  root: {
    display: 'flex',
    flexDirection: 'column',
    width: '100vw',
    height: '100vh',
    backgroundColor: tokens.colorNeutralBackground2, // Windows 11 masaüstü gibi hafif bir gri
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    ...shorthands.padding('8px 20px'), // <PERSON>ha az padding
    backgroundColor: tokens.colorBrandBackground, // Windows aksan rengi
    color: tokens.colorNeutralForegroundOnBrand,
    boxShadow: tokens.shadow8, // Daha belirgin bir gölge
    ...shorthands.borderRadius('0px'), // Keskin köşeler (veya sadece alt köşeler yuvarlak)
  },
  headerLeft: {
    display: 'flex',
    alignItems: 'center',
    ...shorthands.gap('12px'), // Logo/isim arası boşluk
  },
  headerRight: {
    display: 'flex',
    alignItems: 'center',
    ...shorthands.gap('12px'), // Kullanıcı/çıkış arası boşluk
  },
  mainLayout: {
    display: 'flex',
    flexGrow: 1,
    // Windows 11 pencereleri gibi içerik alanı çevresinde boşluk bırak
    ...shorthands.padding('10px'),
  },
  sidebar: {
    display: 'flex',
    flexDirection: 'column',
    ...shorthands.padding('12px'),
    backgroundColor: tokens.colorNeutralBackground1, // Sağ taraftaki hafif gri panel gibi
    boxShadow: tokens.shadow8, // Daha belirgin gölge
    ...shorthands.borderRadius(tokens.borderRadiusMedium), // Yuvarlak köşeler
    marginRight: '10px', // Ana içerikten ayırmak için
    minWidth: '220px', // Genişletilmiş yan menü
    ...shorthands.gap('8px'),
  },
  content: {
    flexGrow: 1,
    ...shorthands.padding('24px'), // İçerik içinde padding
    backgroundColor: tokens.colorNeutralBackground1, // İçerik arka planı beyaz
    ...shorthands.borderRadius(tokens.borderRadiusLarge), // İçerik alanının da yuvarlak köşeleri
    boxShadow: tokens.shadow4, // Hafif bir gölge
  },
  footer: {
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center',
    ...shorthands.padding('8px 20px'),
    backgroundColor: tokens.colorNeutralBackground1,
    boxShadow: tokens.shadow8,
    color: tokens.colorNeutralForeground2,
    marginTop: '10px',
    ...shorthands.borderRadius(tokens.borderRadiusMedium),
  },
  toolbar: {
    backgroundColor: tokens.colorNeutralBackground1, // Toolbar arka planı
    boxShadow: tokens.shadow2, // Hafif gölge
    marginBottom: '10px',
    ...shorthands.padding('8px'),
    ...shorthands.gap('8px'),
    ...shorthands.borderRadius(tokens.borderRadiusMedium),
  },
  tabList: {
    ...shorthands.padding('0px'), // TabList'in kendisi padding almasın, Tab'lar alsın
  },
});

interface MainDashboardProps {
  onLogout: () => void;
}

const MainDashboard: React.FC<MainDashboardProps> = ({ onLogout }) => {
  const styles = useStyles();

  return (
    <div className={styles.root}>
      {/* Header / Başlık Çubuğu */}
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <Text weight="bold" size={500}>Atropos POS</Text>
          <Text size={200}>v1.0</Text>
        </div>
        <div className={styles.headerRight}>
          {/* Daha Windows 11 gibi bir kullanıcı avatarı ve durum ikonları eklenebilir */}
          <Text>Kullanıcı: Admin</Text> {/* Buraya dinamik kullanıcı adı gelecek */}
          <Button onClick={onLogout} appearance="subtle" icon={<ArrowExitFilled />}>Çıkış Yap</Button>
        </div>
      </div>

      <div className={styles.mainLayout}>
        {/* Sol Menü / Sidebar (Windows Başlat Menüsü Konsepti) */}
        <div className={styles.sidebar}>
          <TabList defaultSelectedValue="home" vertical className={styles.tabList}>
            <Tab value="home" icon={<HomeFilled />}>Ana Sayfa</Tab>
            <Tab value="orders" icon={<ListFilled />}>Siparişler</Tab>
            <Tab value="tables" icon={<GridFilled />}>Masalar</Tab> {/* İkon değişti */}
            <Tab value="payments" icon={<MoneyFilled />}>Ödemeler</Tab>
            <Tab value="products" icon={<FoodFilled />}>Ürünler</Tab>
            <Tab value="customers" icon={<PeopleFilled />}>Müşteriler</Tab>
            <Tab value="inventory" icon={<BoxFilled />}>Envanter</Tab> {/* İkon değişti */}
            <Tab value="reports" icon={<DataBarVerticalFilled />}>Raporlar</Tab> {/* İkon değişti */}
            <Tab value="settings" icon={<SettingsFilled />}>Ayarlar</Tab>
          </TabList>
        </div>

        {/* Ana İçerik Alanı */}
        <div className={styles.content}>
          <Toolbar className={styles.toolbar}>
            <ToolbarButton icon={<HomeFilled />}>Dashboard</ToolbarButton>
            <ToolbarDivider />
            <ToolbarButton icon={<ListFilled />}>Yeni Sipariş</ToolbarButton>
            <ToolbarButton icon={<GridFilled />}>Masa Yönetimi</ToolbarButton> {/* İkon değişti */}
          </Toolbar>
          <Text size={500} weight="bold">Ana Ekran İçeriği Buraya Gelecek</Text>
          <p>Modülleri seçerek ilgili içeriği burada görüntüleyeceğiz.</p>
          {/* Buraya seçilen modülün içeriği gelecek */}
        </div>
      </div>

      {/* Alt Bilgi / Footer (Windows Görev Çubuğu Sağ Köşe Benzeri) */}
      <div className={styles.footer}>
        <Text size={200}>Atropos POS v1.0 | {new Date().getFullYear()}</Text>
      </div>
    </div>
  );
};

export default MainDashboard;
