// src/online-platform/dto/create-online-platform.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  IsInt,
  Min,
  IsUrl,
  IsNumber,
  Max,
  IsJSON,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateOnlinePlatformDto {
  @IsString()
  @IsNotEmpty()
  companyId: string;

  @IsString()
  @IsNotEmpty()
  name: string; // "Yemeksep<PERSON>", "Getir", "Trendyol"

  @IsString()
  @IsNotEmpty()
  code: string; // "YS", "GTR", "TRD"

  @IsString()
  @IsOptional()
  @IsUrl()
  apiUrl?: string;

  @IsString()
  @IsOptional()
  apiKey?: string; // encrypted

  @IsString()
  @IsOptional()
  apiSecret?: string; // encrypted

  @IsString()
  @IsOptional()
  merchantId?: string;

  @IsString()
  @IsOptional()
  storeId?: string;

  @IsBoolean()
  @IsOptional()
  active?: boolean;

  @IsBoolean()
  @IsOptional()
  autoAccept?: boolean; // Otomatik sipariş kabulü

  @IsInt()
  @Min(0)
  @IsOptional()
  autoReject?: number; // Dakika sonra otomatik red

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  @IsOptional()
  commissionRate?: number; // Yüzde olarak

  @IsString()
  @IsOptional()
  commissionType?: string; // "PERCENTAGE", "FIXED"

  @IsBoolean()
  @IsOptional()
  syncProducts?: boolean;

  @IsInt()
  @Min(0)
  @IsOptional()
  syncInterval?: number; // Dakika

  // lastSyncAt DateTime? servis tarafından yönetilecek

  @IsOptional()
  workingHours?: any; // JSON (Platform özel çalışma saatleri)
}
