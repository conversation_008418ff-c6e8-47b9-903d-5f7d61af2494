// src/category/category.service.ts
import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';

@Injectable()
export class CategoryService {
  constructor(private prisma: PrismaService) {}

  async createCategory(data: CreateCategoryDto) {
    // Aynı şirket içinde aynı isimde kategori var mı kontrol et
    const existingCategory = await this.prisma.category.findFirst({
      where: {
        companyId: data.companyId,
        name: data.name,
        deletedAt: null // Sadece silinmemiş kategorileri kontrol et
      },
    });

    if (existingCategory) {
      throw new ConflictException(`Category with name "${data.name}" already exists for this company.`);
    }

    // Eğer parentId varsa, parent kategorinin mevcut olduğunu doğrula
    if (data.parentId) {
      const parentExists = await this.prisma.category.findUnique({
        where: { id: data.parentId, deletedAt: null },
      });
      if (!parentExists) {
        throw new NotFoundException(`Parent category with ID "${data.parentId}" not found.`);
      }
    }

    return this.prisma.category.create({ data });
  }

  async findAllCategories(companyId?: string, parentId?: string) {
    return this.prisma.category.findMany({
      where: {
        companyId: companyId || undefined,
        parentId: parentId === null ? null : (parentId || undefined), // parentId null ise sadece ana kategorileri getir
        deletedAt: null,
      },
      include: {
        parent: {
            select: { id: true, name: true } // Sadece parent ID ve adını getir
        },
        children: {
            select: { id: true, name: true } // Alt kategorilerin ID ve adlarını getir
        },
        // printerGroup: true, // Eğer istersen PrinterGroup bilgilerini de çekebilirsin
      },
      orderBy: { displayOrder: 'asc' }, // Sıralama
    });
  }

  async findOneCategory(id: string) {
    const category = await this.prisma.category.findUnique({
      where: { id, deletedAt: null },
      include: {
        parent: {
            select: { id: true, name: true }
        },
        children: {
            select: { id: true, name: true }
        },
      },
    });
    if (!category) {
      throw new NotFoundException(`Category with ID "${id}" not found.`);
    }
    return category;
  }

  async updateCategory(id: string, data: UpdateCategoryDto) {
    // Eğer parentId güncelleniyorsa, yeni parent kategorinin mevcut olduğunu doğrula
    if (data.parentId !== undefined && data.parentId !== null) {
      const parentExists = await this.prisma.category.findUnique({
        where: { id: data.parentId, deletedAt: null },
      });
      if (!parentExists) {
        throw new NotFoundException(`Parent category with ID "${data.parentId}" not found.`);
      }
    }

    // Aynı şirket içinde aynı isimde başka bir kategoriyle çakışma var mı kontrol et
    if (data.name) {
        const currentCategory = await this.findOneCategory(id); // Güncel kategoriyi al
        const existingCategory = await this.prisma.category.findFirst({
            where: {
                companyId: currentCategory.companyId, // Mevcut kategorinin şirket ID'si
                name: data.name,
                id: { not: id }, // Kendi ID'si hariç
                deletedAt: null
            }
        });
        if (existingCategory) {
            throw new ConflictException(`Category with name "${data.name}" already exists for this company.`);
        }
    }

    try {
      return await this.prisma.category.update({
        where: { id, deletedAt: null },
        data,
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Category with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removeCategory(id: string) {
    // Bu kategoriye bağlı ürün var mı kontrol et
    const productsCount = await this.prisma.product.count({
        where: { categoryId: id, deletedAt: null }
    });

    if (productsCount > 0) {
        throw new ConflictException(`Category with ID "${id}" cannot be deleted because it has ${productsCount} active products.`);
    }

    // Bu kategoriye bağlı alt kategori var mı kontrol et
    const childCategoriesCount = await this.prisma.category.count({
        where: { parentId: id, deletedAt: null }
    });

    if (childCategoriesCount > 0) {
        throw new ConflictException(`Category with ID "${id}" cannot be deleted because it has ${childCategoriesCount} active subcategories.`);
    }

    // Soft delete uygulaması
    try {
      return await this.prisma.category.update({
        where: { id, deletedAt: null },
        data: { deletedAt: new Date(), active: false, showInMenu: false, showInKitchen: false }, // Soft delete ve pasif hale getirme
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Category with ID "${id}" not found or already deleted.`);
      }
      throw error;
    }
  }
}
