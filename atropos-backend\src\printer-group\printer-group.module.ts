// src/printer-group/printer-group.module.ts
import { Module } from '@nestjs/common';
import { PrinterGroupService } from './printer-group.service';
import { PrinterGroupController } from './printer-group.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [PrinterGroupController],
  providers: [PrinterGroupService],
  exports: [PrinterGroupService], // Printer modülü bağımlı olacağı için
})
export class PrinterGroupModule {}
