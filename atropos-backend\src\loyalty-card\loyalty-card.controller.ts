// src/loyalty-card/loyalty-card.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { LoyaltyCardService } from './loyalty-card.service';
import { CreateLoyaltyCardDto } from './dto/create-loyalty-card.dto';
import { UpdateLoyaltyCardDto } from './dto/update-loyalty-card.dto';

@Controller('loyalty-card')
export class LoyaltyCardController {
  constructor(private readonly loyaltyCardService: LoyaltyCardService) {}

  @Post() // POST /loyalty-card
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createLoyaltyCardDto: CreateLoyaltyCardDto) {
    return this.loyaltyCardService.createLoyaltyCard(createLoyaltyCardDto);
  }

  @Get() // GET /loyalty-card?customerId=...&cardNumber=...&cardType=...
  findAll(
    @Query('customerId') customerId?: string,
    @Query('cardNumber') cardNumber?: string,
    @Query('cardType') cardType?: string,
  ) {
    return this.loyaltyCardService.findAllLoyaltyCards(customerId, cardNumber, cardType);
  }

  @Get(':id') // GET /loyalty-card/:id
  findOne(@Param('id') id: string) {
    return this.loyaltyCardService.findOneLoyaltyCard(id);
  }

  @Patch(':id') // PATCH /loyalty-card/:id
  update(@Param('id') id: string, @Body() updateLoyaltyCardDto: UpdateLoyaltyCardDto) {
    return this.loyaltyCardService.updateLoyaltyCard(id, updateLoyaltyCardDto);
  }

  @Delete(':id') // DELETE /loyalty-card/:id (Soft delete - kartı pasif hale getirir)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.loyaltyCardService.removeLoyaltyCard(id);
  }

  // Yardımcı endpoint'ler
  @Post(':id/add-points') // POST /loyalty-card/:id/add-points
  addPoints(@Param('id') id: string, @Body() body: { points: number }) {
    return this.loyaltyCardService.addPoints(id, body.points);
  }

  @Post(':id/spend-points') // POST /loyalty-card/:id/spend-points
  spendPoints(@Param('id') id: string, @Body() body: { points: number }) {
    return this.loyaltyCardService.spendPoints(id, body.points);
  }

  @Post(':id/add-balance') // POST /loyalty-card/:id/add-balance
  addBalance(@Param('id') id: string, @Body() body: { amount: number }) {
    return this.loyaltyCardService.addBalance(id, body.amount);
  }

  @Post(':id/spend-balance') // POST /loyalty-card/:id/spend-balance
  spendBalance(@Param('id') id: string, @Body() body: { amount: number }) {
    return this.loyaltyCardService.spendBalance(id, body.amount);
  }

  @Post(':id/block') // POST /loyalty-card/:id/block
  blockCard(@Param('id') id: string, @Body() body: { reason: string }) {
    return this.loyaltyCardService.blockCard(id, body.reason);
  }

  @Post(':id/unblock') // POST /loyalty-card/:id/unblock
  unblockCard(@Param('id') id: string) {
    return this.loyaltyCardService.unblockCard(id);
  }

  @Post(':id/activate') // POST /loyalty-card/:id/activate
  activateCard(@Param('id') id: string) {
    return this.loyaltyCardService.activateCard(id);
  }

  @Post(':id/deactivate') // POST /loyalty-card/:id/deactivate
  deactivateCard(@Param('id') id: string) {
    return this.loyaltyCardService.deactivateCard(id);
  }
}
