// src/printer/printer.service.ts
import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreatePrinterDto } from './dto/create-printer.dto';
import { UpdatePrinterDto } from './dto/update-printer.dto';
// PrinterType enum'ını manuel tanımlayalım
enum PrinterType {
  THERMAL = 'THERMAL',
  DOT_MATRIX = 'DOT_MATRIX',
  A4 = 'A4',
}

@Injectable()
export class PrinterService {
  constructor(private prisma: PrismaService) {}

  async createPrinter(data: CreatePrinterDto) {
    // Branch mevcut mu kontrol et
    const branchExists = await this.prisma.branch.findUnique({
      where: { id: data.branchId, deletedAt: null },
    });
    if (!branchExists) {
      throw new NotFoundException(`Branch with ID "${data.branchId}" not found.`);
    }

    // PrinterGroup belirtilmişse mevcut mu kontrol et
    if (data.printerGroupId) {
      const printerGroupExists = await this.prisma.printerGroup.findUnique({
        where: { id: data.printerGroupId },
      });
      if (!printerGroupExists) {
        throw new NotFoundException(`Printer group with ID "${data.printerGroupId}" not found.`);
      }
    }
    
    // Ağ yazıcıları için IP adresi gerekli mi kontrol et
    if (data.connectionType === 'NETWORK' && !data.ipAddress) {
        throw new BadRequestException('IP Address is required for NETWORK connection type.');
    }

    // Aynı şube içinde aynı isimde yazıcı var mı kontrol et
    const existingPrinterByName = await this.prisma.printer.findFirst({
        where: { branchId: data.branchId, name: data.name },
    });
    if (existingPrinterByName) {
        throw new ConflictException(`Printer with name "${data.name}" already exists for this branch.`);
    }

    // Ağ yazıcıları için aynı IP ve port kombinasyonu aynı şubede var mı kontrol et
    if (data.connectionType === 'NETWORK' && data.ipAddress && data.port) {
        const existingNetworkPrinter = await this.prisma.printer.findFirst({
            where: {
                branchId: data.branchId,
                connectionType: 'NETWORK',
                ipAddress: data.ipAddress,
                port: data.port,
            },
        });
        if (existingNetworkPrinter) {
            throw new ConflictException(`Network printer with IP "${data.ipAddress}" and port "${data.port}" already exists for this branch.`);
        }
    }

    return this.prisma.printer.create({ data });
  }

  async findAllPrinters(branchId?: string, printerGroupId?: string, type?: PrinterType, active?: boolean) {
    return this.prisma.printer.findMany({
      where: {
        branchId: branchId || undefined,
        printerGroupId: printerGroupId || undefined,
        type: type || undefined,
        active: active !== undefined ? active : undefined,
      },
      include: {
        branch: { select: { id: true, name: true } },
        printerGroup: { select: { id: true, name: true } },
      },
      orderBy: { name: 'asc' },
    });
  }

  async findOnePrinter(id: string) {
    const printer = await this.prisma.printer.findUnique({
      where: { id },
      include: {
        branch: { select: { id: true, name: true } },
        printerGroup: { select: { id: true, name: true } },
      },
    });
    if (!printer) {
      throw new NotFoundException(`Printer with ID "${id}" not found.`);
    }
    return printer;
  }

  async updatePrinter(id: string, data: UpdatePrinterDto) {
    const existingPrinter = await this.findOnePrinter(id);

    // Branch veya PrinterGroup güncelleniyorsa varlıklarını kontrol et
    if (data.branchId && data.branchId !== existingPrinter.branchId) {
        const branchExists = await this.prisma.branch.findUnique({ where: { id: data.branchId, deletedAt: null } });
        if (!branchExists) { throw new NotFoundException(`Branch with ID "${data.branchId}" not found.`); }
    }
    if (data.printerGroupId && data.printerGroupId !== existingPrinter.printerGroupId) {
        const printerGroupExists = await this.prisma.printerGroup.findUnique({ where: { id: data.printerGroupId } });
        if (!printerGroupExists) { throw new NotFoundException(`Printer group with ID "${data.printerGroupId}" not found.`); }
    }

    // Ağ yazıcısıysa IP adresi gerekli mi kontrol et
    const targetConnectionType = data.connectionType || existingPrinter.connectionType;
    const targetIpAddress = data.ipAddress || existingPrinter.ipAddress;
    if (targetConnectionType === 'NETWORK' && !targetIpAddress) {
        throw new BadRequestException('IP Address is required for NETWORK connection type.');
    }

    // Aynı şube içinde aynı isimde yazıcı var mı kontrol et
    if (data.name && data.name !== existingPrinter.name) {
        const existingPrinterByName = await this.prisma.printer.findFirst({
            where: { branchId: data.branchId || existingPrinter.branchId, name: data.name },
        });
        if (existingPrinterByName && existingPrinterByName.id !== id) {
            throw new ConflictException(`Printer with name "${data.name}" already exists for this branch.`);
        }
    }

    // Ağ yazıcıları için aynı IP ve port kombinasyonu aynı şubede var mı kontrol et
    if (targetConnectionType === 'NETWORK' && targetIpAddress && (data.port !== undefined || existingPrinter.port !== undefined)) {
        const targetPort = data.port !== undefined ? data.port : existingPrinter.port;
        const existingNetworkPrinter = await this.prisma.printer.findFirst({
            where: {
                branchId: data.branchId || existingPrinter.branchId,
                connectionType: 'NETWORK',
                ipAddress: targetIpAddress,
                port: targetPort,
                id: { not: id }
            },
        });
        if (existingNetworkPrinter) {
            throw new ConflictException(`Network printer with IP "${targetIpAddress}" and port "${targetPort}" already exists for this branch.`);
        }
    }

    try {
      return await this.prisma.printer.update({
        where: { id },
        data,
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Printer with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removePrinter(id: string) {
    // Printer modelinde deletedAt alanı yok, bu nedenle fiziksel silme yapıyoruz.
    try {
      return await this.prisma.printer.delete({
        where: { id },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Printer with ID "${id}" not found.`);
      }
      throw error;
    }
  }
}
