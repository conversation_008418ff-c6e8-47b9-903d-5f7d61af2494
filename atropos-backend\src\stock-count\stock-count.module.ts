// src/stock-count/stock-count.module.ts
import { Module } from '@nestjs/common';
import { StockCountService } from './stock-count.service';
import { StockCountController } from './stock-count.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { StockMovementModule } from '../stock-movement/stock-movement.module'; // StockMovementService'i kullanmak için

@Module({
  imports: [PrismaModule, StockMovementModule],
  controllers: [StockCountController],
  providers: [StockCountService],
  exports: [StockCountService], // Diğer modüller tarafından kullanılabilir
})
export class StockCountModule {}
