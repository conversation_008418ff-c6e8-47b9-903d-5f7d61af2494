// src/order-log/order-log.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { OrderLogService } from './order-log.service';
import { CreateOrderLogDto } from './dto/create-order-log.dto';
import { UpdateOrderLogDto } from './dto/update-order-log.dto';
import { ParseOptionalDatePipe } from '../common/pipes/parse-optional-date.pipe'; // Tarih <PERSON>'ı

@Controller('order-log')
export class OrderLogController {
  constructor(private readonly orderLogService: OrderLogService) {}

  @Post() // POST /order-log
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createOrderLogDto: CreateOrderLogDto) {
    return this.orderLogService.createOrderLog(createOrderLogDto);
  }

  @Get() // GET /order-log?orderId=...&userId=...&action=...&startDate=...&endDate=...
  findAll(
    @Query('orderId') orderId?: string,
    @Query('userId') userId?: string,
    @Query('action') action?: string,
    @Query('startDate', ParseOptionalDatePipe) startDate?: Date,
    @Query('endDate', ParseOptionalDatePipe) endDate?: Date,
  ) {
    return this.orderLogService.findAllOrderLogs(
      orderId,
      userId,
      action,
      startDate,
      endDate,
    );
  }

  @Get(':id') // GET /order-log/:id
  findOne(@Param('id') id: string) {
    return this.orderLogService.findOneOrderLog(id);
  }

  @Patch(':id') // PATCH /order-log/:id (Forbidden olmalı)
  update(@Param('id') id: string, @Body() updateOrderLogDto: UpdateOrderLogDto) {
    return this.orderLogService.updateOrderLog(id, updateOrderLogDto);
  }

  @Delete(':id') // DELETE /order-log/:id (Forbidden olmalı)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.orderLogService.removeOrderLog(id);
  }
}
