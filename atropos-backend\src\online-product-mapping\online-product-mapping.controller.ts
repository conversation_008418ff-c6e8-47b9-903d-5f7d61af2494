// src/online-product-mapping/online-product-mapping.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { OnlineProductMappingService } from './online-product-mapping.service';
import { CreateOnlineProductMappingDto } from './dto/create-online-product-mapping.dto';
import { UpdateOnlineProductMappingDto } from './dto/update-online-product-mapping.dto';

@Controller('online-product-mapping')
export class OnlineProductMappingController {
  constructor(private readonly onlineProductMappingService: OnlineProductMappingService) {}

  @Post() // POST /online-product-mapping
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createOnlineProductMappingDto: CreateOnlineProductMappingDto) {
    return this.onlineProductMappingService.createOnlineProductMapping(createOnlineProductMappingDto);
  }

  @Get() // GET /online-product-mapping?platformId=...&productId=...
  findAll(
    @Query('platformId') platformId?: string,
    @Query('productId') productId?: string,
  ) {
    return this.onlineProductMappingService.findAllOnlineProductMappings(platformId, productId);
  }

  @Get(':id') // GET /online-product-mapping/:id
  findOne(@Param('id') id: string) {
    return this.onlineProductMappingService.findOneOnlineProductMapping(id);
  }

  @Patch(':id') // PATCH /online-product-mapping/:id
  update(@Param('id') id: string, @Body() updateOnlineProductMappingDto: UpdateOnlineProductMappingDto) {
    return this.onlineProductMappingService.updateOnlineProductMapping(id, updateOnlineProductMappingDto);
  }

  @Delete(':id') // DELETE /online-product-mapping/:id (Fiziksel silme)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.onlineProductMappingService.removeOnlineProductMapping(id);
  }
}
