// src/loyalty-transaction/loyalty-transaction.module.ts
import { Modu<PERSON> } from '@nestjs/common';
import { LoyaltyTransactionService } from './loyalty-transaction.service';
import { LoyaltyTransactionController } from './loyalty-transaction.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { LoyaltyCardModule } from '../loyalty-card/loyalty-card.module'; // LoyaltyCardService'i kullanmak için

@Module({
  imports: [PrismaModule, LoyaltyCardModule], // LoyaltyCardModule'ü import et
  controllers: [LoyaltyTransactionController],
  providers: [LoyaltyTransactionService],
  exports: [LoyaltyTransactionService],
})
export class LoyaltyTransactionModule {}
