// src/payment/payment.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { PaymentService } from './payment.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { UpdatePaymentDto, RefundPaymentDto } from './dto/update-payment.dto';
import { PaymentStatus } from '../../generated/prisma';

@Controller('payment')
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) {}

  @Post() // POST /payment
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createPaymentDto: CreatePaymentDto) {
    return this.paymentService.createPayment(createPaymentDto);
  }

  @Get() // GET /payment?orderId=...&paymentMethodId=...&status=...
  findAll(
    @Query('orderId') orderId?: string,
    @Query('paymentMethodId') paymentMethodId?: string,
    @Query('status') status?: PaymentStatus,
  ) {
    return this.paymentService.findAllPayments(orderId, paymentMethodId, status);
  }

  @Get(':id') // GET /payment/:id
  findOne(@Param('id') id: string) {
    return this.paymentService.findOnePayment(id);
  }

  // PATCH yerine refund işlemi için ayrı bir endpoint
  @Patch(':id/refund') // PATCH /payment/:id/refund
  refund(@Param('id') id: string, @Body() refundDto: RefundPaymentDto) {
    return this.paymentService.refundPayment(id, refundDto);
  }

  // Klasik PATCH (eğer Payment kaydının diğer alanlarını direkt güncelleme ihtiyacı olursa)
  // Şimdilik sadece refund'u öne çıkarıyoruz.
  // @Patch(':id')
  // update(@Param('id') id: string, @Body() updatePaymentDto: UpdatePaymentDto) {
  //   return this.paymentService.updatePayment(id, updatePaymentDto);
  // }

  @Delete(':id') // DELETE /payment/:id (Soft delete)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.paymentService.removePayment(id);
  }
}
