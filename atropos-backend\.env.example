# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/atropos_pos?schema=public"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="24h"

# Application Configuration
NODE_ENV="development"
PORT=3000

# CORS Configuration
FRONTEND_URL="http://localhost:5173"

# E-Archive Configuration (Optional)
E_ARCHIVE_USERNAME=""
E_ARCHIVE_PASSWORD=""
E_INVOICE_USERNAME=""
E_INVOICE_PASSWORD=""

# SMS Provider Configuration (Optional)
SMS_PROVIDER="netgsm"
SMS_API_KEY=""
SMS_API_SECRET=""
SMS_SENDER_NAME=""

# Email Configuration (Optional)
SMTP_HOST=""
SMTP_PORT=587
SMTP_USER=""
SMTP_PASS=""
SMTP_FROM=""

# File Upload Configuration
MAX_FILE_SIZE="10mb"
UPLOAD_PATH="./uploads"

# Redis Configuration (Optional)
REDIS_HOST="localhost"
REDIS_PORT=6379
REDIS_PASSWORD=""

# Logging Configuration
LOG_LEVEL="debug"
LOG_FILE_PATH="./logs"

# Security Configuration
BCRYPT_ROUNDS=10
SESSION_SECRET="your-session-secret-change-this"

# Payment Gateway Configuration (Optional)
PAYMENT_GATEWAY_URL=""
PAYMENT_GATEWAY_API_KEY=""
PAYMENT_GATEWAY_SECRET=""

# Backup Configuration
BACKUP_PATH="./backups"
AUTO_BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=30

# Monitoring Configuration (Optional)
SENTRY_DSN=""
NEW_RELIC_LICENSE_KEY=""

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
