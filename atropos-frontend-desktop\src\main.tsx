// atropos-frontend-desktop/src/main.tsx
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { FluentProvider } from '@fluentui/react-components'; // webLightTheme'ı kaldır
import { atroposTheme } from './themes/atroposTheme'; // atroposTheme'i import et
import './index.css';
import App from './App.tsx';

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <FluentProvider theme={atroposTheme}> {/* Burayı güncelle */}
      <App />
    </FluentProvider>
  </StrictMode>,
);
