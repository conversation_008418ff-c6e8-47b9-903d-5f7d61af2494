// src/loyalty-transaction/dto/update-loyalty-transaction.dto.ts
import { PartialType } from '@nestjs/mapped-types';
import { CreateLoyaltyTransactionDto } from './create-loyalty-transaction.dto';
import { IsString, IsOptional, IsInt, Min, IsNumber, IsDateString } from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateLoyaltyTransactionDto extends PartialType(CreateLoyaltyTransactionDto) {
    // LoyaltyTransaction'lar genellikle güncellenmez. Ancak ADJUSMENT gibi tipler için
    // description veya expiresAt gibi alanlar güncellenebilir.
    // Hassas finansal kayıtlar olduğu için ana alanların güncellenmesi yasaktır.
    // Bu DTO'da genel olarak tüm alanlar opsiyonel bırakılsa da,
    // servis içinde belirli alanların güncellenmesine izin verilecek.
}
