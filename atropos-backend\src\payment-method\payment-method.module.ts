// src/payment-method/payment-method.module.ts
import { Module } from '@nestjs/common';
import { PaymentMethodService } from './payment-method.service';
import { PaymentMethodController } from './payment-method.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [PaymentMethodController],
  providers: [PaymentMethodService],
  exports: [PaymentMethodService], // Payment modülü PaymentMethodService'e bağımlı olacağı için dışa aktarıyoruz
})
export class PaymentMethodModule {}
