// src/inventory-item/dto/create-inventory-item.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsNumber,
  Min,
  IsBoolean,
  IsEnum,
  IsDateString,
} from 'class-validator';
import { Type } from 'class-transformer';
// ProductUnit enum'unu manuel olarak tanımlayalım
enum ProductUnit {
  PIECE = 'PIECE',
  KG = 'KG',
  GRAM = 'GRAM',
  LITER = 'LITER',
  ML = 'ML',
}

export class CreateInventoryItemDto {
  @IsString()
  @IsOptional() // nullable → raw material
  productId?: string; // Hangi nihai ürüne ait olduğu (eğer nihai ürünün kendisiyse)

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  code: string; // Benzersiz kod

  @IsString()
  @IsOptional()
  barcode?: string;

  @IsEnum(ProductUnit)
  @IsNotEmpty()
  unit: ProductUnit;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 3 })
  @Min(0)
  @IsOptional()
  currentStock?: number; // Mevcut stok miktarı, varsayılan 0 olabilir

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 3 })
  @Min(0)
  @IsOptional()
  reservedStock?: number; // Rezerve edilmiş stok, varsayılan 0 olabilir

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 3 })
  @Min(0)
  @IsOptional()
  availableStock?: number; // Mevcut stok - rezerve stok, servis hesaplayabilir

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 3 })
  @Min(0)
  @IsOptional()
  criticalLevel?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 3 })
  @Min(0)
  @IsOptional()
  optimalLevel?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  lastCost?: number; // Son alış maliyeti

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  averageCost?: number; // Ortalama maliyet

  @IsString()
  @IsOptional()
  supplier?: string;

  @IsString()
  @IsOptional()
  supplierCode?: string;

  @IsString()
  @IsOptional()
  location?: string; // Depo konumu

  @IsOptional()
  @IsDateString()
  expiryDate?: Date;

  @IsBoolean()
  @IsOptional()
  active?: boolean;
}
