// src/loyalty-card/loyalty-card.service.ts
import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateLoyaltyCardDto } from './dto/create-loyalty-card.dto';
import { UpdateLoyaltyCardDto } from './dto/update-loyalty-card.dto';
import * as bcrypt from 'bcryptjs'; // bcrypt'i import et

@Injectable()
export class LoyaltyCardService {
  constructor(private prisma: PrismaService) {}

  async createLoyaltyCard(data: CreateLoyaltyCardDto) {
    // Müşteri mevcut mu kontrol et
    const customerExists = await this.prisma.customer.findUnique({
      where: { id: data.customerId, deletedAt: null },
    });
    if (!customerExists) {
      throw new NotFoundException(`Customer with ID "${data.customerId}" not found.`);
    }

    // Bir müşterinin sadece bir sadakat kartı olabilir (customerId unique kısıtlaması)
    const existingCardByCustomer = await this.prisma.loyaltyCard.findUnique({
      where: { customerId: data.customerId },
    });
    if (existingCardByCustomer) {
      throw new ConflictException(`Loyalty card already exists for customer with ID "${data.customerId}".`);
    }

    // Kart numarası benzersiz mi kontrol et
    const existingCardByNumber = await this.prisma.loyaltyCard.findUnique({
      where: { cardNumber: data.cardNumber },
    });
    if (existingCardByNumber) {
      throw new ConflictException(`Loyalty card with number "${data.cardNumber}" already exists.`);
    }

    // PIN varsa hashle
    let hashedPin: string | undefined;
    if (data.pin) {
      hashedPin = await bcrypt.hash(data.pin, 10);
    }

    const createdCard = await this.prisma.loyaltyCard.create({
      data: {
        ...data,
        pin: hashedPin,
        points: data.points !== undefined ? data.points : 0,
        totalEarnedPoints: data.totalEarnedPoints !== undefined ? data.totalEarnedPoints : 0,
        totalSpentPoints: data.totalSpentPoints !== undefined ? data.totalSpentPoints : 0,
        balance: data.balance !== undefined ? parseFloat(data.balance.toFixed(2)) : 0,
        totalLoaded: data.totalLoaded !== undefined ? parseFloat(data.totalLoaded.toFixed(2)) : 0,
        discountRate: data.discountRate !== undefined ? parseFloat(data.discountRate.toFixed(2)) : 0,
        issuedAt: data.issuedAt || new Date(), // Varsayılan değer
      },
    });

    // Return without PIN for security
    const { pin, ...cardWithoutPin } = createdCard;
    return cardWithoutPin;
  }

  async findAllLoyaltyCards(customerId?: string, cardNumber?: string, cardType?: string) {
    return this.prisma.loyaltyCard.findMany({
      where: {
        customerId: customerId || undefined,
        cardNumber: cardNumber || undefined,
        cardType: cardType as any || undefined, // Type casting required for enum filter
        active: true, // Sadece aktif kartları getir
      },
      select: { // PIN'i dışarıda bırak ve diğer alanları seç
        id: true,
        customerId: true,
        cardNumber: true,
        cardType: true,
        points: true,
        totalEarnedPoints: true,
        totalSpentPoints: true,
        balance: true,
        totalLoaded: true,
        discountRate: true,
        issuedAt: true,
        activatedAt: true,
        expiresAt: true,
        blocked: true,
        blockReason: true,
        active: true,
        lastUsedAt: true,
        customer: { select: { id: true, firstName: true, lastName: true, phone: true } },
      },
      orderBy: { id: 'desc' },
    });
  }

  async findOneLoyaltyCard(id: string) {
    const card = await this.prisma.loyaltyCard.findUnique({
      where: { id, active: true }, // Sadece aktif kartları getir
      select: { // PIN'i dışarıda bırak ve diğer alanları seç
        id: true,
        customerId: true,
        cardNumber: true,
        cardType: true,
        points: true,
        totalEarnedPoints: true,
        totalSpentPoints: true,
        balance: true,
        totalLoaded: true,
        discountRate: true,
        issuedAt: true,
        activatedAt: true,
        expiresAt: true,
        blocked: true,
        blockReason: true,
        active: true,
        lastUsedAt: true,
        customer: { select: { id: true, firstName: true, lastName: true, phone: true } },
      },
    });
    if (!card) {
      throw new NotFoundException(`Loyalty card with ID "${id}" not found or not active.`);
    }
    return card;
  }

  async updateLoyaltyCard(id: string, data: UpdateLoyaltyCardDto) {
    const existingCard = await this.findOneLoyaltyCard(id); // Kartın aktif ve mevcut olduğundan emin ol

    // Müşteri ID'si güncelleniyorsa, yeni müşterinin varlığını ve kartının olmadığını kontrol et
    if (data.customerId && data.customerId !== existingCard.customerId) {
        const customerExists = await this.prisma.customer.findUnique({ where: { id: data.customerId, deletedAt: null } });
        if (!customerExists) {
            throw new NotFoundException(`Customer with ID "${data.customerId}" not found.`);
        }
        const existingCardForNewCustomer = await this.prisma.loyaltyCard.findUnique({ where: { customerId: data.customerId } });
        if (existingCardForNewCustomer) {
            throw new ConflictException(`Loyalty card already exists for customer with ID "${data.customerId}".`);
        }
    }

    // Kart numarası güncelleniyorsa benzersiz mi kontrol et
    if (data.cardNumber && data.cardNumber !== existingCard.cardNumber) {
      const existingCardByNumber = await this.prisma.loyaltyCard.findUnique({
        where: { cardNumber: data.cardNumber },
      });
      if (existingCardByNumber && existingCardByNumber.id !== id) {
        throw new ConflictException(`Loyalty card with number "${data.cardNumber}" already exists.`);
      }
    }

    // PIN güncelleniyorsa hashle
    if (data.pin) {
      data.pin = await bcrypt.hash(data.pin, 10);
    }

    try {
      const updatedCard = await this.prisma.loyaltyCard.update({
        where: { id, active: true }, // Sadece aktif kartları güncelle
        data: {
            ...data,
            balance: data.balance !== undefined ? parseFloat(data.balance.toFixed(2)) : undefined,
            totalLoaded: data.totalLoaded !== undefined ? parseFloat(data.totalLoaded.toFixed(2)) : undefined,
            discountRate: data.discountRate !== undefined ? parseFloat(data.discountRate.toFixed(2)) : undefined,
        },
      });

      // Return without PIN for security
      const { pin, ...cardWithoutPin } = updatedCard;
      return cardWithoutPin;
    } catch (error) {
      if ((error as any).code === 'P2025') {
        throw new NotFoundException(`Loyalty card with ID "${id}" not found or not active.`);
      }
      throw error;
    }
  }

  async removeLoyaltyCard(id: string) {
    // LoyaltyCard modelinde deletedAt alanı yok.
    // Genellikle sadakat kartları fiziksel olarak silinmez, "blocked" veya "inactive" yapılır.
    // Şemada 'active' ve 'blocked' alanları olduğu için, bu metot kartı pasif hale getirecek
    // ve blocked olarak işaretleyecek (soft delete benzeri).
    try {
      return await this.prisma.loyaltyCard.update({
        where: { id },
        data: { active: false, blocked: true, blockReason: 'Card removed/terminated by admin.' },
        select: { id: true, active: true, blocked: true, blockReason: true }
      });
    } catch (error) {
      if ((error as any).code === 'P2025') {
        throw new NotFoundException(`Loyalty card with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  // Yardımcı Metotlar (LoyaltyTransaction modülü için)
  async addPoints(cardId: string, pointsToAdd: number) {
    const updatedCard = await this.prisma.loyaltyCard.update({
      where: { id: cardId },
      data: {
        points: { increment: pointsToAdd },
        totalEarnedPoints: { increment: pointsToAdd },
        lastUsedAt: new Date(),
      },
    });

    // Return without PIN for security
    const { pin, ...cardWithoutPin } = updatedCard;
    return cardWithoutPin;
  }

  async spendPoints(cardId: string, pointsToSpend: number) {
    const card = await this.prisma.loyaltyCard.findUnique({ where: { id: cardId } });
    if (!card || card.points < pointsToSpend) { // points is already a number
      throw new BadRequestException('Insufficient loyalty points.');
    }
    const updatedCard = await this.prisma.loyaltyCard.update({
      where: { id: cardId },
      data: {
        points: { decrement: pointsToSpend },
        totalSpentPoints: { increment: pointsToSpend },
        lastUsedAt: new Date(),
      },
    });

    // Return without PIN for security
    const { pin, ...cardWithoutPin } = updatedCard;
    return cardWithoutPin;
  }

  async addBalance(cardId: string, amountToAdd: number) {
    const updatedCard = await this.prisma.loyaltyCard.update({
      where: { id: cardId },
      data: {
        balance: { increment: parseFloat(amountToAdd.toFixed(2)) },
        totalLoaded: { increment: parseFloat(amountToAdd.toFixed(2)) },
        lastUsedAt: new Date(),
      },
    });

    // Return without PIN for security
    const { pin, ...cardWithoutPin } = updatedCard;
    return cardWithoutPin;
  }

  async spendBalance(cardId: string, amountToSpend: number) {
    const card = await this.prisma.loyaltyCard.findUnique({ where: { id: cardId } });
    if (!card || parseFloat(card.balance.toString()) < amountToSpend) { // balance is Decimal, convert to number
      throw new BadRequestException('Insufficient balance.');
    }
    const updatedCard = await this.prisma.loyaltyCard.update({
      where: { id: cardId },
      data: {
        balance: { decrement: parseFloat(amountToSpend.toFixed(2)) },
        lastUsedAt: new Date(),
      },
    });

    // Return without PIN for security
    const { pin, ...cardWithoutPin } = updatedCard;
    return cardWithoutPin;
  }

  async blockCard(cardId: string, reason: string) {
    const updatedCard = await this.prisma.loyaltyCard.update({
      where: { id: cardId },
      data: { blocked: true, blockReason: reason },
    });

    // Return without PIN for security
    const { pin, ...cardWithoutPin } = updatedCard;
    return cardWithoutPin;
  }

  async unblockCard(cardId: string) {
    const updatedCard = await this.prisma.loyaltyCard.update({
      where: { id: cardId },
      data: { blocked: false, blockReason: null },
    });

    // Return without PIN for security
    const { pin, ...cardWithoutPin } = updatedCard;
    return cardWithoutPin;
  }

  async activateCard(cardId: string) {
    const updatedCard = await this.prisma.loyaltyCard.update({
      where: { id: cardId },
      data: { active: true, activatedAt: new Date() },
    });

    // Return without PIN for security
    const { pin, ...cardWithoutPin } = updatedCard;
    return cardWithoutPin;
  }

  async deactivateCard(cardId: string) {
    const updatedCard = await this.prisma.loyaltyCard.update({
      where: { id: cardId },
      data: { active: false },
    });

    // Return without PIN for security
    const { pin, ...cardWithoutPin } = updatedCard;
    return cardWithoutPin;
  }
}
