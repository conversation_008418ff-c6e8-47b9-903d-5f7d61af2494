// src/tax/tax.module.ts
import { Module } from '@nestjs/common';
import { TaxService } from './tax.service';
import { TaxController } from './tax.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [TaxController],
  providers: [TaxService],
  exports: [TaxService], // Ürün modülü TaxService'e bağımlı olacağı için dışa aktarıyoruz
})
export class TaxModule {}
