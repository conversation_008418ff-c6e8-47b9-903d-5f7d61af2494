// src/printer/printer.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
  ParseBoolPipe,
} from '@nestjs/common';
import { PrinterService } from './printer.service';
import { CreatePrinterDto } from './dto/create-printer.dto';
import { UpdatePrinterDto } from './dto/update-printer.dto';
// PrinterType enum'ını manuel tanımlayalım
enum PrinterType {
  THERMAL = 'THERMAL',
  DOT_MATRIX = 'DOT_MATRIX',
  A4 = 'A4',
}

@Controller('printer')
export class PrinterController {
  constructor(private readonly printerService: PrinterService) {}

  @Post() // POST /printer
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createPrinterDto: CreatePrinterDto) {
    return this.printerService.createPrinter(createPrinterDto);
  }

  @Get() // GET /printer?branchId=...&printerGroupId=...&type=...&active=...
  findAll(
    @Query('branchId') branchId?: string,
    @Query('printerGroupId') printerGroupId?: string,
    @Query('type') type?: PrinterType,
    @Query('active', new ParseBoolPipe({ optional: true })) active?: boolean,
  ) {
    return this.printerService.findAllPrinters(branchId, printerGroupId, type, active);
  }

  @Get(':id') // GET /printer/:id
  findOne(@Param('id') id: string) {
    return this.printerService.findOnePrinter(id);
  }

  @Patch(':id') // PATCH /printer/:id
  update(@Param('id') id: string, @Body() updatePrinterDto: UpdatePrinterDto) {
    return this.printerService.updatePrinter(id, updatePrinterDto);
  }

  @Delete(':id') // DELETE /printer/:id (Fiziksel silme)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.printerService.removePrinter(id);
  }
}
