// src/inventory-item/inventory-item.module.ts
import { Module } from '@nestjs/common';
import { InventoryItemService } from './inventory-item.service';
import { InventoryItemController } from './inventory-item.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [InventoryItemController],
  providers: [InventoryItemService],
  exports: [InventoryItemService], // Diğer envanter modülleri (Recipe, StockMovement, StockCount) bağımlı olacağı için
})
export class InventoryItemModule {}
