// src/table/dto/create-table.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsInt,
  Min,
  Max,
  IsBoolean,
  IsEnum,
} from 'class-validator';
import { TableShape, TableStatus } from '../../../generated/prisma'; // Generated Prisma'dan enum'ları import et
import { Type } from 'class-transformer';

export class CreateTableDto {
  @IsString()
  @IsNotEmpty()
  branchId: string;

  @IsString()
  @IsOptional()
  areaId?: string; // Masa alanı ID'si

  @IsString()
  @IsNotEmpty()
  number: string; // Masa numarası veya kodu (örn: "M1", "A-5")

  @IsString()
  @IsOptional()
  name?: string; // Özel isim "VIP 1"

  @IsInt()
  @IsOptional()
  @Min(1)
  capacity?: number; // Minimum 1 kişi

  @IsInt()
  @IsOptional()
  @Min(1)
  minCapacity?: number; // Minimum kapasite

  @IsInt()
  @IsOptional()
  positionX?: number; // Görsel düzen için X koordinatı

  @IsInt()
  @IsOptional()
  positionY?: number; // Görsel düzen için Y koordinatı

  @IsInt()
  @IsOptional()
  width?: number; // Görsel düzen için genişlik

  @IsInt()
  @IsOptional()
  height?: number; // Görsel düzen için yükseklik

  @IsEnum(TableShape)
  @IsOptional()
  shape?: TableShape; // Masa şekli (RECTANGLE, CIRCLE vb.)

  @IsEnum(TableStatus)
  @IsOptional()
  status?: TableStatus; // Masa durumu (EMPTY, OCCUPIED vb.)

  // mergedWithIds String[]: Bu, TableMerge modeliyle yönetileceği için DTO'ya eklemiyoruz
  // Ancak ihtiyaç olursa eklenebilir, şimdilik bu karmaşıklığı dışarıda tutuyoruz.

  @IsBoolean()
  @IsOptional()
  isVip?: boolean;

  @IsString()
  @IsOptional()
  qrCode?: string; // QR menü entegrasyonu için

  @IsBoolean()
  @IsOptional()
  active?: boolean;
}
