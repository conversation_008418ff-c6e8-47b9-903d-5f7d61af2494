// src/cash-movement/dto/update-cash-movement.dto.ts
import { PartialType } from '@nestjs/mapped-types';
import { CreateCashMovementDto } from './create-cash-movement.dto';
import {
  IsString,
  IsOptional,
  IsNumber,
  IsEnum,
  IsDateString,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CashMovementType } from '../../../generated/prisma';

export class UpdateCashMovementDto extends PartialType(CreateCashMovementDto) {
  @IsOptional()
  @IsDateString()
  approvedAt?: Date; // Onaylanma tarihi de güncellenebilir
}
