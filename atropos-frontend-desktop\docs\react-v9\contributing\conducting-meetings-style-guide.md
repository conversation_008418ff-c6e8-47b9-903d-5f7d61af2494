# Conducting Meetings Style Guide

> Authors: <AUTHORS>

<!-- to update toc, copy this file to local machine and run `npx markdown-toc -i file-name.md` -->

<!-- toc -->

- [Style Guide](#style-guide)
  - [Agenda](#agenda)
  - [Recording](#recording)
  - [Communication during meeting](#communication-during-meeting)
  - [Meeting roles](#meeting-roles)
    - [Meeting lead/Moderator](#meeting-leadmoderator)
    - [<PERSON><PERSON><PERSON>](#scribe)

<!-- tocstop -->

This document describes best practices on how to conduct meetings within Fluent UI web organization.

**Why:**

Based on conversation between Tech Leads and managers we identified following set of problems:

- A lot of context might be shared during a meeting that is not being followed up or gets lost in huge threads of chat.
- Although we record our meetings, chat is not being “recorded”, video cannot be searched, which adds excessive time allocation overhead to person that didn’t attend but wants/needs to stay in loop or follow up on topic.

Based on these identified issues here is a simple guidance how to conduct meeting.

## Style Guide

### Agenda

Agenda should be provided in advance via Loop Meeting Notes functionality.

> 💡 TIP: write <kbd>//</kbd> + <kbd>ENTER</kbd> to generate your name

- Every Agenda topic ends with name of author

_Example of Agenda item:_

`//@alias / <Topic>` --- translates to --> `💬 @Martin / Write a style guide for drinking beer`

or

`<Topic> / [<Author>]` --- translates to --> `Write a style guide for drinking beer / [Martin]`

### Recording

Meetings should be recorded so it can be viewed by anyone that wasn't able to attend. Recording also helps with transparency. This also lets Copilot transcribe meetings and be an automated scribe.

### Communication during meeting

1. Use Teams meeting “raise hand” feature if one wants to contribute to a topic
2. Use the meeting for side bar type chatter and not the subject at hand. This keeps conversations from forking and being hard to track.. Feel free to use it for memes, pictures, making jokes/fun comments.

#### Meeting Lead/Moderator

- Drives the meeting agenda
- Keeps the meeting inclusive by tracking the chat and ensuring folks who've raised their hands can speak.
- Verbally summarizes the outcome of discussed topic
