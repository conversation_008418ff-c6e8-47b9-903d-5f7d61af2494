// src/branch/branch.service.ts
import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateBranchDto } from './dto/create-branch.dto';
import { UpdateBranchDto } from './dto/update-branch.dto';

@Injectable()
export class BranchService {
  constructor(private prisma: PrismaService) {}

  async createBranch(data: CreateBranchDto) {
    // companyId ve code'un Company bazında unique olduğunu doğrula
    const existingBranch = await this.prisma.branch.findUnique({
      where: {
        companyId_code: {
          companyId: data.companyId,
          code: data.code,
        },
      },
    });

    if (existingBranch) {
      throw new ConflictException(`Branch with code "${data.code}" already exists for this company.`);
    }

    return this.prisma.branch.create({ data });
  }

  async findAllBranches(companyId?: string) {
    // İsteğe bağlı olarak companyId'ye göre filtreleme
    return this.prisma.branch.findMany({
      where: { companyId: companyId || undefined, deletedAt: null }, // Soft delete kontrolü
      include: { company: true }, // İlişkili şirket bilgilerini de getir
    });
  }

  async findOneBranch(id: string) {
    const branch = await this.prisma.branch.findUnique({
      where: { id, deletedAt: null }, // Soft delete kontrolü
      include: { company: true },
    });
    if (!branch) {
      throw new NotFoundException(`Branch with ID "${id}" not found.`);
    }
    return branch;
  }

  async updateBranch(id: string, data: UpdateBranchDto) {
    try {
      // Güncelleme sırasında code veya companyId değişiyorsa unique kontrolü
      if (data.code || data.companyId) {
        const existingBranch = await this.prisma.branch.findUnique({
          where: {
            companyId_code: {
              companyId: data.companyId || (await this.findOneBranch(id)).companyId,
              code: data.code || (await this.findOneBranch(id)).code,
            },
          },
        });
        // Eğer kendi ID'si değilse ve mevcutsa çakışma var demektir.
        if (existingBranch && existingBranch.id !== id) {
          throw new ConflictException(`Branch with code "${data.code}" already exists for this company.`);
        }
      }

      return await this.prisma.branch.update({
        where: { id, deletedAt: null }, // Soft delete kontrolü
        data,
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Branch with ID "${id}" not found.`);
      }
      if (error.code === 'P2002') { // Unique constraint violation (bu durumda elle kontrol ettik ama genel Prisma hata filtresi de yakalar)
        throw new ConflictException(`Branch with this code already exists for the company.`);
      }
      throw error;
    }
  }

  async removeBranch(id: string) {
    // Soft delete uygulaması
    try {
      return await this.prisma.branch.update({
        where: { id, deletedAt: null }, // Sadece aktif olanı sil (soft delete)
        data: { deletedAt: new Date() },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Branch with ID "${id}" not found or already deleted.`);
      }
      throw error;
    }
  }
}
