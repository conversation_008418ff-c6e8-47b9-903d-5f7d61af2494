// src/product/product.module.ts
import { Module } from '@nestjs/common';
import { ProductService } from './product.service';
import { ProductController } from './product.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule], // ProductService Prisma'ya bağımlı olduğu için
  controllers: [ProductController],
  providers: [ProductService],
  exports: [ProductService], // Diğer modüllerde ProductService kullanmak istersen (örn: Order modülü)
})
export class ProductModule {}
