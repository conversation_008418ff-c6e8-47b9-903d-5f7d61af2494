// src/task/task.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,

} from '@nestjs/common';
import { TaskService } from './task.service';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { $Enums } from '../../generated/prisma';
import { ParseOptionalDatePipe } from '../common/pipes/parse-optional-date.pipe'; // Tarih <PERSON>'ı
import { ParseOptionalEnumPipe } from '../common/pipes/parse-optional-enum.pipe'; // Enum Pipe'ı

@Controller('task')
export class TaskController {
  constructor(private readonly taskService: TaskService) {}

  @Post() // POST /task
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createTaskDto: CreateTaskDto) {
    return this.taskService.createTask(createTaskDto);
  }

  @Get() // GET /task?companyId=...&branchId=...&assignedToId=...&status=...&priority=...&startDate=...&endDate=...
  findAll(
    @Query('companyId') companyId?: string,
    @Query('branchId') branchId?: string,
    @Query('assignedToId') assignedToId?: string,
    @Query('status', new ParseOptionalEnumPipe($Enums.TaskStatus)) status?: $Enums.TaskStatus,
    @Query('priority', new ParseOptionalEnumPipe($Enums.TaskPriority)) priority?: $Enums.TaskPriority,
    @Query('startDate', ParseOptionalDatePipe) startDate?: Date,
    @Query('endDate', ParseOptionalDatePipe) endDate?: Date,
  ) {
    return this.taskService.findAllTasks(
      companyId,
      branchId,
      assignedToId,
      status,
      priority,
      startDate,
      endDate,
    );
  }

  @Get(':id') // GET /task/:id
  findOne(@Param('id') id: string) {
    return this.taskService.findOneTask(id);
  }

  @Patch(':id') // PATCH /task/:id
  update(@Param('id') id: string, @Body() updateTaskDto: UpdateTaskDto) {
    return this.taskService.updateTask(id, updateTaskDto);
  }

  @Delete(':id') // DELETE /task/:id (Durumunu değiştir)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.taskService.removeTask(id);
  }
}
