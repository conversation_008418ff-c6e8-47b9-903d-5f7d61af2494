// src/price-override/price-override.service.ts
import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreatePriceOverrideDto } from './dto/create-price-override.dto';
import { UpdatePriceOverrideDto } from './dto/update-price-override.dto';

@Injectable()
export class PriceOverrideService {
  constructor(private prisma: PrismaService) {}

  async createPriceOverride(data: CreatePriceOverrideDto) {
    // İlişkili varlıkların varlığını kontrol et
    const branchExists = await this.prisma.branch.findUnique({ where: { id: data.branchId, deletedAt: null } });
    if (!branchExists) { throw new NotFoundException(`Branch with ID "${data.branchId}" not found.`); }
    const productExists = await this.prisma.product.findUnique({ where: { id: data.productId, deletedAt: null } });
    if (!productExists) { throw new NotFoundException(`Product with ID "${data.productId}" not found.`); }
    if (data.variantId) {
        const variantExists = await this.prisma.productVariant.findUnique({ where: { id: data.variantId, productId: data.productId, deletedAt: null } });
        if (!variantExists) { throw new NotFoundException(`Product variant with ID "${data.variantId}" not found for product "${data.productId}".`); }
    }
    const createdByExists = await this.prisma.user.findUnique({ where: { id: data.createdBy, deletedAt: null } });
    if (!createdByExists) { throw new NotFoundException(`User (createdBy) with ID "${data.createdBy}" not found.`); }

    // Çakışan fiyat geçersiz kılma var mı kontrol et (aynı şube, ürün/varyant, zaman aralığı)
    const newStartDate = new Date(data.startDate);
    const newEndDate = data.endDate ? new Date(data.endDate) : null;

    const overlappingOverrides = await this.prisma.priceOverride.findMany({
        where: {
            branchId: data.branchId,
            productId: data.productId,
            variantId: data.variantId || null, // Varyantı belirtilmemiş fiyatlar da çakışabilir
            OR: [
                // Yeni aralığın başlangıcı mevcut aralığın içindeyse
                {
                    AND: [
                        { startDate: { lt: newEndDate || new Date('9999-12-31T23:59:59Z') } }, // Eğer endDate null ise sonsuz olarak düşün
                        { endDate: { gt: newStartDate } },
                    ],
                },
                // Mevcut aralığın başlangıcı yeni aralığın içindeyse
                {
                    AND: [
                        { startDate: { lt: newStartDate } },
                        { endDate: { gt: newStartDate } },
                    ],
                },
                // Yeni aralık mevcut aralığı tamamen kapsıyorsa
                {
                    AND: [
                        { startDate: { gte: newStartDate } },
                        { endDate: { lte: newEndDate || new Date('9999-12-31T23:59:59Z') } },
                    ],
                }
            ],
        },
    });

    if (overlappingOverrides.length > 0) {
        throw new ConflictException('Overlapping price override already exists for this product/variant in the specified time range.');
    }

    return this.prisma.priceOverride.create({
      data: {
        ...data,
        overridePrice: parseFloat(data.overridePrice.toFixed(2)),
        startDate: newStartDate,
        endDate: newEndDate,
      },
    });
  }

  async findAllPriceOverrides(branchId?: string, productId?: string, variantId?: string, activeOnly?: boolean, date?: Date) {
    return this.prisma.priceOverride.findMany({
      where: {
        branchId: branchId || undefined,
        productId: productId || undefined,
        variantId: variantId || undefined,

        AND: [ // Aktiflik ve tarih filtresi
            activeOnly !== undefined ? {
                startDate: { lte: date || new Date() },
                OR: [
                    { endDate: { gte: date || new Date() } },
                    { endDate: null }
                ]
            } : {},
        ]
      },
      include: {
        branch: { select: { id: true, name: true } },
        product: { select: { id: true, name: true, code: true } },
        variant: { select: { id: true, name: true, code: true } },
      },
      orderBy: { startDate: 'desc' },
    });
  }

  async findOnePriceOverride(id: string) {
    const override = await this.prisma.priceOverride.findUnique({
      where: { id },
      include: {
        branch: { select: { id: true, name: true } },
        product: { select: { id: true, name: true, code: true } },
        variant: { select: { id: true, name: true, code: true } },
      },
    });
    if (!override) {
      throw new NotFoundException(`Price override with ID "${id}" not found.`);
    }
    return override;
  }

  async updatePriceOverride(id: string, data: UpdatePriceOverrideDto) {
    const existingOverride = await this.findOnePriceOverride(id);

    // İlişkili varlıkların güncelliğini kontrol et
    if (data.branchId && data.branchId !== existingOverride.branchId) {
        const branchExists = await this.prisma.branch.findUnique({ where: { id: data.branchId, deletedAt: null } });
        if (!branchExists) { throw new NotFoundException(`Branch with ID "${data.branchId}" not found.`); }
    }
    // productId, variantId, createdBy için benzer kontroller...

    // Çakışan fiyat geçersiz kılma var mı kontrol et (güncelleme sırasında)
    const newStartDate = data.startDate ? new Date(data.startDate) : existingOverride.startDate;
    const newEndDate = data.endDate ? new Date(data.endDate) : existingOverride.endDate;

    const targetBranchId = data.branchId || existingOverride.branchId;
    const targetProductId = data.productId || existingOverride.productId;
    const targetVariantId = data.variantId === undefined ? existingOverride.variantId : data.variantId; // null olarak da gelebilir

    const overlappingOverrides = await this.prisma.priceOverride.findMany({
        where: {
            branchId: targetBranchId,
            productId: targetProductId,
            variantId: targetVariantId,
            id: { not: id }, // Kendi kaydımızı hariç tut
            OR: [
                {
                    AND: [
                        { startDate: { lt: newEndDate || new Date('9999-12-31T23:59:59Z') } },
                        { endDate: { gt: newStartDate } },
                    ],
                },
                {
                    AND: [
                        { startDate: { lt: newStartDate } },
                        { endDate: { gt: newStartDate } },
                    ],
                },
                {
                    AND: [
                        { startDate: { gte: newStartDate } },
                        { endDate: { lte: newEndDate || new Date('9999-12-31T23:59:59Z') } },
                    ],
                }
            ],
        },
    });

    if (overlappingOverrides.length > 0) {
        throw new ConflictException('Overlapping price override already exists for this product/variant in the updated time range.');
    }

    try {
      return await this.prisma.priceOverride.update({
        where: { id },
        data: {
            ...data,
            overridePrice: data.overridePrice !== undefined ? parseFloat(data.overridePrice.toFixed(2)) : undefined,
            startDate: data.startDate ? new Date(data.startDate) : undefined,
            endDate: data.endDate ? new Date(data.endDate) : undefined,
        },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Price override with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removePriceOverride(id: string) {
    // PriceOverride modelinde deletedAt alanı yok.
    // Genellikle fiyat geçersiz kılmaları silinmez, sadece bitiş tarihleri güncellenir.
    // Bu metod sadece fiziksel silme yapacak, dikkatli kullanılmalı.
    try {
      return await this.prisma.priceOverride.delete({
        where: { id },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Price override with ID "${id}" not found.`);
      }
      throw error;
    }
  }
}
