// src/category/dto/create-category.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  IsInt,
  Min,
} from 'class-validator';

export class CreateCategoryDto {
  @IsString()
  @IsNotEmpty()
  companyId: string; // Hangi şirkete bağlı olduğu

  @IsString()
  @IsOptional()
  parentId?: string; // Ana kategori ID'si (alt kategori için)

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  image?: string;

  @IsString()
  @IsOptional()
  color?: string; // Renk kodu (örn: #RRGGBB)

  @IsString()
  @IsOptional()
  icon?: string; // İkon adı veya URL'si

  @IsBoolean()
  @IsOptional()
  showInKitchen?: boolean; // KDS'de gösterilecek mi

  @IsInt()
  @IsOptional()
  @Min(0)
  preparationTime?: number; // Dakika olarak hazırlık süresi

  @IsInt()
  @IsOptional()
  @Min(0)
  displayOrder?: number; // Görüntülenme sırası

  @IsBoolean()
  @IsOptional()
  active?: boolean;

  @IsBoolean()
  @IsOptional()
  showInMenu?: boolean; // QR menüde göster

  @IsString()
  @IsOptional()
  printerGroupId?: string; // Hangi yazıcı grubuna bağlı olduğu
}
