// src/online-product-mapping/online-product-mapping.service.ts
import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateOnlineProductMappingDto } from './dto/create-online-product-mapping.dto';
import { UpdateOnlineProductMappingDto } from './dto/update-online-product-mapping.dto';

@Injectable()
export class OnlineProductMappingService {
  constructor(private prisma: PrismaService) {}

  async createOnlineProductMapping(data: CreateOnlineProductMappingDto) {
    // OnlinePlatform mevcut mu kontrol et
    const platformExists = await this.prisma.onlinePlatform.findUnique({
      where: { id: data.platformId }, // OnlinePlatform'da deletedAt yok
    });
    if (!platformExists) {
      throw new NotFoundException(`Online platform with ID "${data.platformId}" not found.`);
    }

    // Kendi Product'ımız mevcut mu kontrol et
    const productExists = await this.prisma.product.findUnique({
      where: { id: data.productId, deletedAt: null },
    });
    if (!productExists) {
      throw new NotFoundException(`Product with ID "${data.productId}" not found.`);
    }

    // Aynı platformda, aynı kendi ürünümüz için zaten bir eşleşme var mı?
    const existingMappingByProductId = await this.prisma.onlineProductMapping.findUnique({
      where: {
        platformId_productId: {
          platformId: data.platformId,
          productId: data.productId,
        },
      },
    });
    if (existingMappingByProductId) {
      throw new ConflictException(`Product mapping already exists for product "${data.productId}" on platform "${data.platformId}".`);
    }

    // Aynı platformda, aynı platform ürünü ID'si ile zaten bir eşleşme var mı?
    const existingMappingByPlatformProductId = await this.prisma.onlineProductMapping.findUnique({
      where: {
        platformId_platformProductId: {
          platformId: data.platformId,
          platformProductId: data.platformProductId,
        },
      },
    });
    if (existingMappingByPlatformProductId) {
      throw new ConflictException(`Platform product ID "${data.platformProductId}" is already mapped on platform "${data.platformId}".`);
    }
    
    return this.prisma.onlineProductMapping.create({
      data: {
        ...data,
        priceOverride: data.priceOverride !== undefined ? parseFloat(data.priceOverride.toFixed(2)) : undefined,
      },
    });
  }

  async findAllOnlineProductMappings(platformId?: string, productId?: string) {
    return this.prisma.onlineProductMapping.findMany({
      where: {
        platformId: platformId || undefined,
        productId: productId || undefined,
      },
      include: {
        platform: { select: { id: true, name: true, code: true } },
        product: { select: { id: true, name: true, code: true, basePrice: true } },
      },
      orderBy: { id: 'desc' },
    });
  }

  async findOneOnlineProductMapping(id: string) {
    const mapping = await this.prisma.onlineProductMapping.findUnique({
      where: { id },
      include: {
        platform: { select: { id: true, name: true, code: true } },
        product: { select: { id: true, name: true, code: true, basePrice: true } },
      },
    });
    if (!mapping) {
      throw new NotFoundException(`Online product mapping with ID "${id}" not found.`);
    }
    return mapping;
  }

  async updateOnlineProductMapping(id: string, data: UpdateOnlineProductMappingDto) {
    // İlişkili Platform veya Ürün güncelleniyorsa varlıklarını kontrol et
    if (data.platformId) {
        const platformExists = await this.prisma.onlinePlatform.findUnique({ where: { id: data.platformId } });
        if (!platformExists) {
            throw new NotFoundException(`Online platform with ID "${data.platformId}" not found.`);
        }
    }
    if (data.productId) {
        const productExists = await this.prisma.product.findUnique({ where: { id: data.productId, deletedAt: null } });
        if (!productExists) {
            throw new NotFoundException(`Product with ID "${data.productId}" not found.`);
        }
    }

    // platformProductId veya productId güncelleniyorsa benzersizlik kontrolü
    if (data.platformId || data.productId || data.platformProductId) {
        const currentMapping = await this.findOneOnlineProductMapping(id);
        const targetPlatformId = data.platformId || currentMapping.platformId;

        // Kendi ürünümüzle eşleşme çakışması
        if (data.productId && data.productId !== currentMapping.productId) { // Eğer productId değişiyorsa
            const existingMappingByProductId = await this.prisma.onlineProductMapping.findUnique({
                where: { platformId_productId: { platformId: targetPlatformId, productId: data.productId } },
            });
            if (existingMappingByProductId && existingMappingByProductId.id !== id) {
                throw new ConflictException(`Product mapping already exists for product "${data.productId}" on platform "${targetPlatformId}".`);
            }
        }

        // Platform ürünü ID'si çakışması
        if (data.platformProductId && data.platformProductId !== currentMapping.platformProductId) { // Eğer platformProductId değişiyorsa
            const existingMappingByPlatformProductId = await this.prisma.onlineProductMapping.findUnique({
                where: { platformId_platformProductId: { platformId: targetPlatformId, platformProductId: data.platformProductId } },
            });
            if (existingMappingByPlatformProductId && existingMappingByPlatformProductId.id !== id) {
                throw new ConflictException(`Platform product ID "${data.platformProductId}" is already mapped on platform "${targetPlatformId}".`);
            }
        }
    }

    try {
      return await this.prisma.onlineProductMapping.update({
        where: { id },
        data: {
            ...data,
            priceOverride: data.priceOverride !== undefined ? parseFloat(data.priceOverride.toFixed(2)) : undefined,
        },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Online product mapping with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removeOnlineProductMapping(id: string) {
    // OnlineProductMapping modelinde soft delete alanı yok, bu nedenle fiziksel silme yapıyoruz.
    try {
      return await this.prisma.onlineProductMapping.delete({
        where: { id },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Online product mapping with ID "${id}" not found.`);
      }
      throw error;
    }
  }
}
