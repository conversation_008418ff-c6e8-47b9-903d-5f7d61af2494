// src/online-platform/online-platform.module.ts
import { Module } from '@nestjs/common';
import { OnlinePlatformService } from './online-platform.service';
import { OnlinePlatformController } from './online-platform.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [OnlinePlatformController],
  providers: [OnlinePlatformService],
  exports: [OnlinePlatformService], // OnlineOrder ve OnlineProductMapping modülleri bağımlı olacağı için
})
export class OnlinePlatformModule {}
