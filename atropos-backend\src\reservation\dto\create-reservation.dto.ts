// src/reservation/dto/create-reservation.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsDateString,
  IsInt,
  Min,
  IsEnum,
  IsArray,
  ArrayMinSize,
  Max,
  IsNumber,
  IsEmail,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ReservationStatus, ReservationSource } from '../../../generated/prisma'; // Enum'ları import et

export class CreateReservationDto {
  @IsString()
  @IsNotEmpty()
  branchId: string;

  @IsString()
  @IsOptional()
  customerId?: string; // Kayıtlı müşteri ID'si

  @IsString()
  @IsNotEmpty()
  customerName: string;

  @IsString()
  @IsNotEmpty()
  phone: string;

  @IsString()
  @IsOptional()
  @IsEmail()
  customerEmail?: string;

  @IsDateString()
  @IsNotEmpty()
  reservationDate: Date; // Rezervasyon tarihi (sadece tarih)

  @IsString()
  @IsNotEmpty()
  reservationTime: string; // "HH:MM" formatında rezervasyon saati

  @IsInt()
  @Min(1)
  @IsOptional()
  duration?: number; // Dakika olarak süre, varsayılan 120 (2 saat)

  @IsInt()
  @Min(1)
  @IsNotEmpty()
  guestCount: number; // Misafir sayısı

  @IsInt()
  @Min(0)
  @IsOptional()
  childCount?: number;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tableIds?: string[]; // Ayrılan masa ID'leri (Birden fazla masa için)

  @IsString()
  @IsOptional()
  tablePreference?: string; // "window", "garden", "quiet"

  @IsEnum(ReservationStatus)
  @IsOptional()
  status?: ReservationStatus; // Varsayılan: PENDING

  @IsString()
  @IsOptional()
  specialRequests?: string; // Müşteri istekleri

  @IsString()
  @IsOptional()
  allergyInfo?: string; // Alerji bilgileri

  @IsString()
  @IsOptional()
  occasionType?: string; // "birthday", "anniversary"

  @IsString()
  @IsOptional()
  internalNotes?: string; // İç notlar

  @IsEnum(ReservationSource)
  @IsOptional()
  source?: ReservationSource; // Rezervasyon kaynağı (PHONE, WEBSITE vb.)

  @IsString()
  @IsOptional()
  confirmationCode?: string;

  @IsString()
  @IsOptional()
  confirmedBy?: string; // Onaylayan kullanıcı ID'si

  @IsBoolean()
  @IsOptional()
  depositRequired?: boolean;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  depositAmount?: number;

  @IsBoolean()
  @IsOptional()
  depositPaid?: boolean;

  // reminderSent, reminderSentAt servis tarafından yönetilecek

  // confirmedAt, cancelledAt, seatedAt, completedAt servis tarafından yönetilecek

  @IsString()
  @IsOptional()
  cancelReason?: string;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  noShowFee?: number;

  @IsString()
  @IsOptional()
  createdBy?: string; // Rezervasyonu oluşturan kullanıcı ID'si
}
