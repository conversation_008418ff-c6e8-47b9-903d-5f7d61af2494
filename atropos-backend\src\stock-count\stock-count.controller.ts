// src/stock-count/stock-count.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { StockCountService } from './stock-count.service';
import { CreateStockCountDto } from './dto/create-stock-count.dto';
import { UpdateStockCountDto } from './dto/update-stock-count.dto';
import { StockCountType, StockCountStatus } from '../../generated/prisma';

@Controller('stock-count')
export class StockCountController {
  constructor(private readonly stockCountService: StockCountService) {}

  @Post() // POST /stock-count
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createStockCountDto: CreateStockCountDto) {
    return this.stockCountService.createStockCount(createStockCountDto);
  }

  @Get() // GET /stock-count?branchId=...&countType=...&status=...&startDate=...&endDate=...
  findAll(
    @Query('branchId') branchId?: string,
    @Query('countType') countType?: StockCountType,
    @Query('status') status?: StockCountStatus,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.stockCountService.findAllStockCounts(
      branchId,
      countType as any,
      status,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
    );
  }

  @Get(':id') // GET /stock-count/:id
  findOne(@Param('id') id: string) {
    return this.stockCountService.findOneStockCount(id);
  }

  @Patch(':id') // PATCH /stock-count/:id
  update(@Param('id') id: string, @Body() updateStockCountDto: UpdateStockCountDto) {
    return this.stockCountService.updateStockCount(id, updateStockCountDto);
  }

  @Delete(':id') // DELETE /stock-count/:id (Fiziksel silme)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.stockCountService.removeStockCount(id);
  }
}
