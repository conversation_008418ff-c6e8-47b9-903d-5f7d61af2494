// src/reservation/reservation.service.ts
import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateReservationDto } from './dto/create-reservation.dto';
import { UpdateReservationDto } from './dto/update-reservation.dto';
import { ReservationStatus } from '../../generated/prisma'; // Enum'ları import et

@Injectable()
export class ReservationService {
  constructor(private prisma: PrismaService) {}

  async createReservation(data: CreateReservationDto) {
    // Branch, Customer (eğer belirtilmiş<PERSON>), CreatedBy (eğer belirtilmişse) var mı kontrol et
    const branchExists = await this.prisma.branch.findUnique({ where: { id: data.branchId, deletedAt: null } });
    if (!branchExists) { throw new NotFoundException(`Branch with ID "${data.branchId}" not found.`); }
    if (data.customerId) {
        const customerExists = await this.prisma.customer.findUnique({ where: { id: data.customerId, deletedAt: null } });
        if (!customerExists) { throw new NotFoundException(`Customer with ID "${data.customerId}" not found.`); }
    }
    if (data.createdBy) {
        const createdByExists = await this.prisma.user.findUnique({ where: { id: data.createdBy, deletedAt: null } });
        if (!createdByExists) { throw new NotFoundException(`User (createdBy) with ID "${data.createdBy}" not found.`); }
    }
    if (data.confirmedBy) {
        const confirmedByExists = await this.prisma.user.findUnique({ where: { id: data.confirmedBy, deletedAt: null } });
        if (!confirmedByExists) { throw new NotFoundException(`User (confirmedBy) with ID "${data.confirmedBy}" not found.`); }
    }

    // Masa ID'leri belirtilmişse varlıklarını ve uygunluklarını kontrol et
    if (data.tableIds && data.tableIds.length > 0) {
        const tables = await this.prisma.table.findMany({
            where: { id: { in: data.tableIds }, branchId: data.branchId, deletedAt: null },
        });
        if (tables.length !== data.tableIds.length) {
            const foundIds = tables.map(t => t.id);
            const notFoundIds = data.tableIds.filter(id => !foundIds.includes(id));
            throw new NotFoundException(`Some tables not found or not in specified branch: ${notFoundIds.join(', ')}`);
        }
        // Masaların şu anda müsait olup olmadığını kontrol et
        const occupiedTables = tables.filter(t => t.status !== 'EMPTY' && t.status !== 'CLEANING');
        if (occupiedTables.length > 0) {
            throw new ConflictException(`Some selected tables are not available: ${occupiedTables.map(t => t.number).join(', ')}. Status: ${occupiedTables.map(t => t.status).join(', ')}`);
        }
        // Masaların kapasitesinin misafir sayısını karşılayıp karşılamadığını kontrol et (basit toplam kapasite kontrolü)
        const totalCapacity = tables.reduce((sum, t) => sum + t.capacity, 0);
        if (data.guestCount > totalCapacity) {
            throw new BadRequestException(`Selected tables' total capacity (${totalCapacity}) is insufficient for ${data.guestCount} guests.`);
        }
    }
    
    // Aynı şube, tarih ve saat aralığı için çakışan rezervasyon var mı kontrol et
    // Rezervasyon tarih-saat hesaplama - Düzeltildi
    const resDate = new Date(data.reservationDate);
    resDate.setUTCHours(0, 0, 0, 0); // Sadece tarihi baz alıyorsak UTC başlangıcını ayarla

    const [hours, minutes] = data.reservationTime.split(':').map(Number);
    const reservationStartDateTime = new Date(
      resDate.getFullYear(),
      resDate.getMonth(),
      resDate.getDate(),
      hours,
      minutes,
      0,
      0
    );

    const reservationEndDateTime = new Date(reservationStartDateTime.getTime() + (data.duration || 120) * 60 * 1000); // Varsayılan 120 dakika

    const overlappingReservations = await this.prisma.reservation.findMany({
        where: {
            branchId: data.branchId,
            status: { notIn: ['CANCELLED', 'NO_SHOW', 'COMPLETED'] }, // İptal edilmemiş, tamamlanmamış, gelmemiş rezervasyonlar
            reservationDate: resDate, // Aynı tarih
            tableIds: { hasSome: data.tableIds || [] } // Eğer masalar belirtilmişse, aynı masaları kullananları kontrol et
        },
    });

    if (overlappingReservations.length > 0) {
        // Detaylı çakışma kontrolü (eğer aynı masalar içinse)
        let hasTableConflict = false;
        if (data.tableIds && data.tableIds.length > 0) {
            for (const existingRes of overlappingReservations) {
                const existingResTables = new Set(existingRes.tableIds);
                for (const newTableId of data.tableIds) {
                    if (existingResTables.has(newTableId)) {
                        hasTableConflict = true;
                        break;
                    }
                }
                if (hasTableConflict) break;
            }
        }
        if (hasTableConflict) {
             throw new ConflictException('Selected tables are already reserved for this time slot.');
        }
        // Eğer masa bazında çakışma yoksa, genel zaman çakışması uyarısı verilebilir.
        // Örneğin: "Another reservation exists for this time slot, but different tables."
        // Şimdilik sadece masa çakışmasına odaklanalım.
    }

    const reservation = await this.prisma.reservation.create({
      data: {
        branchId: data.branchId,
        customerId: data.customerId,
        customerName: data.customerName,
        customerPhone: data.phone,
        customerEmail: data.customerEmail,
        reservationDate: resDate,
        reservationTime: data.reservationTime,
        reservationStartDateTime,
        reservationEndDateTime,
        duration: data.duration || 120, // Varsayılan süre
        guestCount: data.guestCount,
        childCount: data.childCount || 0,
        tableIds: data.tableIds || [],
        tablePreference: data.tablePreference,
        status: data.status || ReservationStatus.PENDING,
        specialRequests: data.specialRequests,
        allergyInfo: data.allergyInfo,
        occasionType: data.occasionType,
        internalNotes: data.internalNotes,
        source: data.source || 'PHONE',
        confirmationCode: data.confirmationCode,
        confirmedBy: data.confirmedBy,
        depositRequired: data.depositRequired || false,
        depositAmount: data.depositAmount !== undefined ? parseFloat(data.depositAmount.toFixed(2)) : undefined,
        depositPaid: data.depositPaid || false,
        cancelReason: data.cancelReason,
        noShowFee: data.noShowFee !== undefined ? parseFloat(data.noShowFee.toFixed(2)) : undefined,
        createdBy: data.createdBy,
      },
      include: { customer: true, branch: true, },
    });

    // Masaların durumunu 'RESERVED' olarak güncelle
    if (data.tableIds && data.tableIds.length > 0) {
        await this.prisma.table.updateMany({
            where: { id: { in: data.tableIds } },
            data: { status: 'RESERVED' },
        });
    }

    return reservation;
  }

  async findAllReservations(branchId?: string, customerId?: string, status?: ReservationStatus, startDate?: Date, endDate?: Date) {
    return this.prisma.reservation.findMany({
      where: {
        branchId: branchId || undefined,
        customerId: customerId || undefined,
        status: status || undefined,
        reservationDate: { // Sadece tarih filtresi için
            gte: startDate ? new Date(startDate.toISOString().split('T')[0]) : undefined,
            lte: endDate ? new Date(endDate.toISOString().split('T')[0] + 'T23:59:59.999Z') : undefined,
        },
        // deletedAt alanı yok
      },
      include: {
        branch: { select: { id: true, name: true } },
        customer: { select: { id: true, firstName: true, lastName: true, phone: true } },
      },
      orderBy: [{ reservationDate: 'asc' }, { reservationTime: 'asc' }],
    });
  }

  async findOneReservation(id: string) {
    const reservation = await this.prisma.reservation.findUnique({
      where: { id }, // deletedAt alanı yok
      include: {
        branch: { select: { id: true, name: true } },
        customer: { select: { id: true, firstName: true, lastName: true, phone: true } },
      },
    });
    if (!reservation) {
      throw new NotFoundException(`Reservation with ID "${id}" not found.`);
    }
    return reservation;
  }

  async updateReservation(id: string, data: UpdateReservationDto) {
    const existingReservation = await this.findOneReservation(id);

    // İptal edilmiş veya tamamlanmış rezervasyonlar güncellenemez
    if (existingReservation.status === ReservationStatus.CANCELLED || existingReservation.status === ReservationStatus.COMPLETED || existingReservation.status === ReservationStatus.NO_SHOW) {
        throw new BadRequestException(`Cannot update a reservation with status "${existingReservation.status}".`);
    }

    // İlişkili varlıkların güncelliğini kontrol et (Branch, Customer, User, Table)
    if ((data as any).branchId && (data as any).branchId !== existingReservation.branchId) {
        const branchExists = await this.prisma.branch.findUnique({ where: { id: (data as any).branchId, deletedAt: null } });
        if (!branchExists) throw new NotFoundException(`Branch with ID "${(data as any).branchId}" not found.`);
    }
    // customerId, createdBy, confirmedBy için benzer kontroller...

    // Masa ID'leri güncelleniyorsa varlıklarını ve uygunluklarını kontrol et
    if ((data as any).tableIds && (data as any).tableIds.length > 0) {
        const tables = await this.prisma.table.findMany({
            where: { id: { in: (data as any).tableIds }, branchId: (data as any).branchId || existingReservation.branchId, deletedAt: null },
        });
        if (tables.length !== (data as any).tableIds.length) {
            const foundIds = tables.map(t => t.id);
            const notFoundIds = (data as any).tableIds.filter(id => !foundIds.includes(id));
            throw new NotFoundException(`Some tables not found or not in specified branch: ${notFoundIds.join(', ')}`);
        }
        // Masaların müsaitlik ve kapasite kontrolünü burada da yap
        const occupiedTables = tables.filter(t => t.status !== 'EMPTY' && t.status !== 'CLEANING' && !existingReservation.tableIds.includes(t.id)); // Zaten rezervasyona ait olmayan, dolu masalar
        if (occupiedTables.length > 0) {
            throw new ConflictException(`Some selected tables are not available: ${occupiedTables.map(t => t.number).join(', ')}. Status: ${occupiedTables.map(t => t.status).join(', ')}`);
        }
        const totalCapacity = tables.reduce((sum, t) => sum + t.capacity, 0);
        if ((data as any).guestCount && (data as any).guestCount > totalCapacity) {
            throw new BadRequestException(`Selected tables' total capacity (${totalCapacity}) is insufficient for ${(data as any).guestCount} guests.`);
        }

        // Eski masaları EMPTY yap, yeni masaları RESERVED yap
        const oldTablesToEmpty = existingReservation.tableIds.filter(oldId => !((data as any).tableIds || []).includes(oldId));
        const newTablesToReserve = ((data as any).tableIds || []).filter(newId => !existingReservation.tableIds.includes(newId));

        if (oldTablesToEmpty.length > 0) {
            await this.prisma.table.updateMany({
                where: { id: { in: oldTablesToEmpty } },
                data: { status: 'EMPTY' },
            });
        }
        if (newTablesToReserve.length > 0) {
            await this.prisma.table.updateMany({
                where: { id: { in: newTablesToReserve } },
                data: { status: 'RESERVED' },
            });
        }
    }

    // Zaman veya masa değişiklikleri varsa çakışma kontrolü (Create'deki benzer mantık)
    if ((data as any).reservationDate || (data as any).reservationTime || (data as any).duration || (data as any).tableIds) {
        const newReservationDate = (data as any).reservationDate || existingReservation.reservationDate;
        const newTableIds = (data as any).tableIds || existingReservation.tableIds;

        const overlappingReservations = await this.prisma.reservation.findMany({
            where: {
                branchId: (data as any).branchId || existingReservation.branchId,
                id: { not: id }, // Kendi rezervasyonumuzu hariç tut
                status: { notIn: ['CANCELLED', 'NO_SHOW', 'COMPLETED'] },
                reservationDate: newReservationDate, // Aynı tarih
                tableIds: { hasSome: newTableIds || [] }
            },
        });

        if (overlappingReservations.length > 0) {
            let hasTableConflict = false;
            if (newTableIds && newTableIds.length > 0) {
                for (const existingRes of overlappingReservations) {
                    const existingResTables = new Set(existingRes.tableIds);
                    for (const newTableId of newTableIds) {
                        if (existingResTables.has(newTableId)) {
                            hasTableConflict = true;
                            break;
                        }
                    }
                    if (hasTableConflict) break;
                }
            }
            if (hasTableConflict) {
                 throw new ConflictException('Selected tables are already reserved for this updated time slot.');
            }
        }
    }

    try {
      const updatedReservation = await this.prisma.reservation.update({
        where: { id },
        data: {
            branchId: (data as any).branchId,
            customerId: (data as any).customerId,
            customerName: (data as any).customerName,
            customerPhone: (data as any).phone,
            customerEmail: (data as any).customerEmail,
            reservationDate: (data as any).reservationDate,
            reservationTime: (data as any).reservationTime,
            duration: (data as any).duration,
            guestCount: (data as any).guestCount,
            childCount: (data as any).childCount,
            tableIds: (data as any).tableIds,
            tablePreference: (data as any).tablePreference,
            status: data.status,
            specialRequests: (data as any).specialRequests,
            allergyInfo: (data as any).allergyInfo,
            occasionType: (data as any).occasionType,
            internalNotes: (data as any).internalNotes,
            source: (data as any).source,
            confirmationCode: (data as any).confirmationCode,
            confirmedBy: (data as any).confirmedBy,
            depositRequired: (data as any).depositRequired,
            depositAmount: (data as any).depositAmount !== undefined ? parseFloat((data as any).depositAmount.toFixed(2)) : undefined,
            depositPaid: (data as any).depositPaid,
            reminderSent: data.reminderSent,
            reminderSentAt: data.reminderSentAt,
            confirmedAt: data.confirmedAt,
            cancelledAt: data.cancelledAt,
            seatedAt: data.seatedAt,
            completedAt: data.completedAt,
            cancelReason: (data as any).cancelReason,
            noShowFee: (data as any).noShowFee !== undefined ? parseFloat((data as any).noShowFee.toFixed(2)) : undefined,
            createdBy: (data as any).createdBy,
        },
      });

      // Rezervasyon durumu değişimine göre masa durumunu güncelle
      if (data.status && updatedReservation.tableIds && updatedReservation.tableIds.length > 0) {
          let newTableStatus: string | undefined;
          switch (data.status) {
              case ReservationStatus.SEATED: newTableStatus = 'OCCUPIED'; break;
              case ReservationStatus.CANCELLED:
              case ReservationStatus.NO_SHOW:
              case ReservationStatus.COMPLETED: newTableStatus = 'EMPTY'; break;
              default: break;
          }
          if (newTableStatus) {
              await this.prisma.table.updateMany({
                  where: { id: { in: updatedReservation.tableIds } },
                  data: { status: newTableStatus as any },
              });
          }
      }

      return updatedReservation;
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Reservation with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removeReservation(id: string) {
    // Reservation modelinde deletedAt alanı yok.
    // Rezervasyonlar genellikle fiziksel olarak silinmez, "CANCELLED" veya "NO_SHOW" olarak işaretlenir.
    // Bu metot, rezervasyonu CANCELLED yapar ve bağlı masaları boşaltır.
    try {
      const reservation = await this.prisma.reservation.update({
        where: { id },
        data: { status: 'CANCELLED', cancelledAt: new Date(), cancelReason: 'Deleted by admin.' },
      });

      // İlişkili masaları EMPTY yap
      if (reservation.tableIds && reservation.tableIds.length > 0) {
        await this.prisma.table.updateMany({
            where: { id: { in: reservation.tableIds } },
            data: { status: 'EMPTY' },
        });
      }
      return reservation;
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Reservation with ID "${id}" not found.`);
      }
      throw error;
    }
  }
}
