// src/cash-movement/cash-movement.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { CashMovementService } from './cash-movement.service';
import { CreateCashMovementDto } from './dto/create-cash-movement.dto';
import { UpdateCashMovementDto } from './dto/update-cash-movement.dto';
import { CashMovementType } from '../../generated/prisma';

@Controller('cash-movement')
export class CashMovementController {
  constructor(private readonly cashMovementService: CashMovementService) {}

  @Post() // POST /cash-movement
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createCashMovementDto: CreateCashMovementDto) {
    return this.cashMovementService.createCashMovement(createCashMovementDto);
  }

  @Get() // GET /cash-movement?branchId=...&userId=...&type=...&startDate=...&endDate=...
  findAll(
    @Query('branchId') branchId?: string,
    @Query('userId') userId?: string,
    @Query('type') type?: CashMovementType,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const parsedStartDate = startDate ? new Date(startDate) : undefined;
    const parsedEndDate = endDate ? new Date(endDate + 'T23:59:59Z') : undefined;

    return this.cashMovementService.findAllCashMovements(
      branchId,
      userId,
      type,
      parsedStartDate,
      parsedEndDate,
    );
  }

  @Get(':id') // GET /cash-movement/:id
  findOne(@Param('id') id: string) {
    return this.cashMovementService.findOneCashMovement(id);
  }

  @Patch(':id') // PATCH /cash-movement/:id
  update(@Param('id') id: string, @Body() updateCashMovementDto: UpdateCashMovementDto) {
    return this.cashMovementService.updateCashMovement(id, updateCashMovementDto);
  }

  @Delete(':id') // DELETE /cash-movement/:id (Fiziksel silme)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.cashMovementService.removeCashMovement(id);
  }
}
