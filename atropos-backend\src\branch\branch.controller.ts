// src/branch/branch.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query, // Query parametresi için
} from '@nestjs/common';
import { BranchService } from './branch.service';
import { CreateBranchDto } from './dto/create-branch.dto';
import { UpdateBranchDto } from './dto/update-branch.dto';

@Controller('branch')
export class BranchController {
  constructor(private readonly branchService: BranchService) {}

  @Post() // POST /branch
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createBranchDto: CreateBranchDto) {
    return this.branchService.createBranch(createBranchDto);
  }

  @Get() // GET /branch?companyId=...
  findAll(@Query('companyId') companyId?: string) {
    return this.branchService.findAllBranches(companyId);
  }

  @Get(':id') // GET /branch/:id
  findOne(@Param('id') id: string) {
    return this.branchService.findOneBranch(id);
  }

  @Patch(':id') // PATCH /branch/:id
  update(@Param('id') id: string, @Body() updateBranchDto: UpdateBranchDto) {
    return this.branchService.updateBranch(id, updateBranchDto);
  }

  @Delete(':id') // DELETE /branch/:id
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.branchService.removeBranch(id);
  }
}
