// src/online-order/dto/update-online-order.dto.ts
import { PartialType } from '@nestjs/mapped-types';
import { CreateOnlineOrderDto } from './create-online-order.dto';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsNumber,
  Min,
  IsBoolean,
  IsDateString,
  IsJSON,
  ValidateNested,
  IsArray,
} from 'class-validator';
import { Type } from 'class-transformer';
// OnlineOrderStatus enum'unu manuel olarak tanımlayalım
enum OnlineOrderStatus {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
  PREPARING = 'PREPARING',
  READY = 'READY',
  DELIVERING = 'DELIVERING',
  DELIVERED = 'DELIVERED',
  CANCELLED = 'CANCELLED',
  RETURNED = 'RETURNED',
}

// Güncelleme sırasında online sipariş kalemi için DTO
class UpdateOnlineOrderItemDto {
  @IsString()
  @IsOptional()
  platformProductId?: string;

  @IsString()
  @IsOptional()
  name?: string;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 3 })
  @Min(0)
  @IsOptional()
  quantity?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  unitPrice?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  totalPrice?: number;

  @IsString()
  @IsOptional()
  note?: string;
}

export class UpdateOnlineOrderDto {
  @IsOptional()
  @IsEnum(OnlineOrderStatus)
  status?: OnlineOrderStatus;

  @IsString()
  @IsOptional()
  platformStatus?: string;

  @IsOptional()
  @IsDateString()
  acceptedAt?: Date;

  @IsOptional()
  @IsDateString()
  rejectedAt?: Date;

  @IsOptional()
  @IsDateString()
  preparingAt?: Date;

  @IsOptional()
  @IsDateString()
  readyAt?: Date;

  @IsOptional()
  @IsDateString()
  deliveringAt?: Date;

  @IsOptional()
  @IsDateString()
  deliveredAt?: Date;

  @IsOptional()
  @IsDateString()
  cancelledAt?: Date;

  @IsString()
  @IsOptional()
  rejectReason?: string;

  @IsString()
  @IsOptional()
  cancelReason?: string;

  @IsOptional()
  @IsBoolean()
  isPaid?: boolean;

  @IsOptional()
  orderData?: any; // JSON verisi güncellemesi
}
