// src/cash-movement/cash-movement.module.ts
import { Module } from '@nestjs/common';
import { CashMovementService } from './cash-movement.service';
import { CashMovementController } from './cash-movement.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [CashMovementController],
  providers: [CashMovementService],
  exports: [CashMovementService], // DailyReport modülü CashMovementService'e bağımlı olacağı için dışa aktarıyoruz
})
export class CashMovementModule {}
