// src/notification-log/dto/create-notification-log.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsJSON,
  IsDateString,
} from 'class-validator';
import { Type } from 'class-transformer';
import { NotificationChannel, NotificationStatus } from '../../../generated/prisma'; // Enum'ları import et

export class CreateNotificationLogDto {
  @IsString()
  @IsNotEmpty()
  templateId: string; // Hangi şablondan gönderildiği

  @IsString()
  @IsNotEmpty()
  recipient: string; // Telefon numarası veya e-posta adresi

  @IsEnum(NotificationChannel)
  @IsNotEmpty()
  channel: NotificationChannel;

  @IsEnum(NotificationStatus)
  @IsOptional()
  status?: NotificationStatus; // Varsayılan: PENDING veya SENT

  @IsString()
  @IsNotEmpty()
  message: string; // Gerçek gönderilen mesaj içeriği

  @IsOptional()
  response?: any; // SMS/Email sağlayıcısından gelen yanıt (JSON)

  // sentAt, deliveredAt, readAt Prisma tarafından yönetilecek veya opsiyonel olarak gönderilebilir

  @IsString()
  @IsOptional()
  failedReason?: string;
}
