// src/recipe/recipe.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { RecipeService } from './recipe.service';
import { CreateRecipeDto } from './dto/create-recipe.dto';
import { UpdateRecipeDto } from './dto/update-recipe.dto';

@Controller('recipe')
export class RecipeController {
  constructor(private readonly recipeService: RecipeService) {}

  @Post() // POST /recipe
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createRecipeDto: CreateRecipeDto) {
    return this.recipeService.createRecipe(createRecipeDto);
  }

  @Get() // GET /recipe?productId=...
  findAll(@Query('productId') productId?: string) {
    return this.recipeService.findAllRecipes(productId);
  }

  @Get(':id') // GET /recipe/:id
  findOne(@Param('id') id: string) {
    return this.recipeService.findOneRecipe(id);
  }

  @Patch(':id') // PATCH /recipe/:id
  update(@Param('id') id: string, @Body() updateRecipeDto: UpdateRecipeDto) {
    return this.recipeService.updateRecipe(id, updateRecipeDto);
  }

  @Delete(':id') // DELETE /recipe/:id (Soft delete)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.recipeService.removeRecipe(id);
  }
}
