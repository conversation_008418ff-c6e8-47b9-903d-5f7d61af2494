// src/printer/dto/create-printer.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsInt,
  Min,
  Max,
  IsEnum,
  IsIP,
  IsBoolean,
} from 'class-validator';
// PrinterType enum'ını manuel tanımlayalım
enum PrinterType {
  THERMAL = 'THERMAL',
  DOT_MATRIX = 'DOT_MATRIX',
  A4 = 'A4',
}

export class CreatePrinterDto {
  @IsString()
  @IsNotEmpty()
  branchId: string; // Hangi şubeye bağlı olduğu

  @IsString()
  @IsOptional()
  printerGroupId?: string; // Hangi yazıcı grubuna ait olduğu

  @IsString()
  @IsNotEmpty()
  name: string; // Yazı<PERSON>ı adı (örn: "Mutfak Yazıcısı 1")

  @IsEnum(PrinterType)
  @IsNotEmpty()
  type: PrinterType; // THERMAL, DOT_MATRIX, A4

  @IsString()
  @IsNotEmpty()
  connectionType: string; // "NETWORK", "USB", "BLUETOOTH"

  @IsString()
  @IsOptional()
  @IsIP('4') // Sadece IPv4 için
  ipAddress?: string; // Ağ yazıcıları için

  @IsInt()
  @Min(1)
  @Max(65535)
  @IsOptional()
  port?: number; // Varsayılan: 9100

  @IsBoolean()
  @IsOptional()
  active?: boolean;
}
