// src/table-merge/table-merge.service.ts
import { Injectable, NotFoundException, ConflictException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateTableMergeDto } from './dto/create-table-merge.dto';
import { UpdateTableMergeDto } from './dto/update-table-merge.dto';
import { TableStatus } from '../../generated/prisma';

@Injectable()
export class TableMergeService {
  constructor(private prisma: PrismaService) {}

  async createTableMerge(data: CreateTableMergeDto) {
    // tableId ve targetId aynı olamaz
    if (data.tableId === data.targetId) {
      throw new BadRequestException('Main table ID and merged table ID cannot be the same.');
    }

    // Masaların varlığını kontrol et
    const mainTable = await this.prisma.table.findUnique({ where: { id: data.tableId, deletedAt: null } });
    const targetTable = await this.prisma.table.findUnique({ where: { id: data.targetId, deletedAt: null } });

    if (!mainTable) { throw new NotFoundException(`Main table with ID "${data.tableId}" not found or deleted.`); }
    if (!targetTable) { throw new NotFoundException(`Merged table with ID "${data.targetId}" not found or deleted.`); }

    // Aynı şubede olup olmadıklarını kontrol et
    if (mainTable.branchId !== targetTable.branchId) {
        throw new BadRequestException('Main table and merged table must be in the same branch.');
    }

    // Ana masa veya hedef masa zaten başka bir birleştirme içinde mi kontrol et
    const existingMergeForMain = await this.prisma.tableMerge.findFirst({
        where: {
            OR: [
                { tableId: data.tableId },
                { targetId: data.tableId }
            ]
        }
    });
    if (existingMergeForMain) {
        throw new ConflictException(`Main table with ID "${data.tableId}" is already part of another merge.`);
    }

    const existingMergeForTarget = await this.prisma.tableMerge.findFirst({
        where: {
            OR: [
                { tableId: data.targetId },
                { targetId: data.targetId }
            ]
        }
    });
    if (existingMergeForTarget) {
        throw new ConflictException(`Merged table with ID "${data.targetId}" is already part of another merge.`);
    }

    // Çift giriş olmaması için (A->B ve B->A)
    const reverseMergeExists = await this.prisma.tableMerge.findFirst({
        where: { tableId: data.targetId, targetId: data.tableId }
    });
    if (reverseMergeExists) {
        throw new ConflictException(`Reverse merge already exists between table "${data.targetId}" and "${data.tableId}".`);
    }

    // Masaların müsaitlik durumunu kontrol et (isteğe bağlı: sadece EMPTY olanlar birleştirilsin gibi)
    // Şimdilik sadece mevcut olanları kontrol ettik.

    const tableMerge = await this.prisma.tableMerge.create({ data });

    // Masaların durumunu güncelle: Ana masa 'OCCUPIED' veya 'MERGED', Hedef masa 'MERGED'
    // Ayrıca, Order modülünü güncellemek gerekebilir (merged table'ın siparişlerini ana masaya aktar)
    await this.prisma.table.update({
        where: { id: data.tableId },
        data: { status: TableStatus.MERGED }, // Ana masayı MERGED yap
    });
    await this.prisma.table.update({
        where: { id: data.targetId },
        data: { status: TableStatus.MERGED }, // Hedef masayı MERGED yap
    });
    
    // Sipariş aktarımı (Karmaşık iş mantığı, şimdilik bu kapsamın dışında)
    // Eğer birleştirilen masanın aktif siparişleri varsa, onları ana masaya taşımak gerekir.
    // await this.prisma.order.updateMany({
    //     where: { tableId: data.targetId, deletedAt: null, status: { notIn: ['COMPLETED', 'CANCELLED', 'RETURNED'] } },
    //     data: { tableId: data.tableId }
    // });

    return tableMerge;
  }

  async findAllTableMerges(tableId?: string, targetId?: string) {
    const whereCondition: any = {};

    if (tableId || targetId) {
      whereCondition.OR = [];
      if (tableId) {
        whereCondition.OR.push({ tableId: tableId });
      }
      if (targetId) {
        whereCondition.OR.push({ targetId: targetId });
      }
    }

    return this.prisma.tableMerge.findMany({
      where: whereCondition,
      include: {
        mainTable: { select: { id: true, number: true, name: true, status: true, branchId: true } },
        mergedTable: { select: { id: true, number: true, name: true, status: true, branchId: true } },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOneTableMerge(id: string) {
    const merge = await this.prisma.tableMerge.findUnique({
      where: { id },
      include: {
        mainTable: { select: { id: true, number: true, name: true, status: true, branchId: true } },
        mergedTable: { select: { id: true, number: true, name: true, status: true, branchId: true } },
      },
    });
    if (!merge) {
      throw new NotFoundException(`Table merge with ID "${id}" not found.`);
    }
    return merge;
  }

  async updateTableMerge(id: string, data: UpdateTableMergeDto) {
    // TableMerge kayıtları genellikle güncellenmez. Bir birleştirme değişecekse,
    // mevcut birleştirme silinip yeni birleştirme oluşturulur.
    // Core alanların (tableId, targetId) güncellenmesine izin vermeyelim.
    throw new ForbiddenException('Updating core fields of a table merge is not allowed. Please delete the existing merge and create a new one.');
    /*
    try {
        return await this.prisma.tableMerge.update({
            where: { id },
            data,
        });
    } catch (error) {
        if (error.code === 'P2025') {
            throw new NotFoundException(`Table merge with ID "${id}" not found.`);
        }
        throw error;
    }
    */
  }

  async removeTableMerge(id: string) {
    // Masa birleştirmesini fiziksel olarak sil (masaları ayır)
    try {
      const merge = await this.prisma.tableMerge.delete({
        where: { id },
      });

      // Birleştirilmiş masaların durumunu tekrar EMPTY yap (veya önceki durumuna dön)
      // Burada basitçe EMPTY yapıyoruz, karmaşık bir sistemde önceki durumuna dönmeli.
      await this.prisma.table.update({
          where: { id: merge.tableId },
          data: { status: TableStatus.EMPTY },
      });
      await this.prisma.table.update({
          where: { id: merge.targetId },
          data: { status: TableStatus.EMPTY },
      });

      // Siparişleri de eski masalara geri taşımak gerekebilir (karmaşık iş mantığı)

      return merge;
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Table merge with ID "${id}" not found.`);
      }
      throw error;
    }
  }
}
