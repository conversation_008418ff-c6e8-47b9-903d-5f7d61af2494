// src/task/task.service.ts
import { Injectable, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { UserRole, $Enums } from '../../generated/prisma'; // TaskStatus, TaskPriority, UserRole enum'ları için

@Injectable()
export class TaskService {
  constructor(private prisma: PrismaService) {}

  async createTask(data: CreateTaskDto) {
    // Company, Branch (eğer belirtilmişse), assignedTo (eğer belirtilmişse), createdBy var mı kontrol et
    const companyExists = await this.prisma.company.findUnique({ where: { id: data.companyId, deletedAt: null } });
    if (!companyExists) { throw new NotFoundException(`Company with ID "${data.companyId}" not found.`); }
    if (data.branchId) {
        const branchExists = await this.prisma.branch.findUnique({ where: { id: data.branchId, deletedAt: null } });
        if (!branchExists) { throw new NotFoundException(`Branch with ID "${data.branchId}" not found.`); }
    }
    if (data.assignedToId) {
        const assignedToUser = await this.prisma.user.findUnique({ where: { id: data.assignedToId, deletedAt: null } });
        if (!assignedToUser) { throw new NotFoundException(`Assigned user with ID "${data.assignedToId}" not found.`); }
    }
    const createdByUser = await this.prisma.user.findUnique({ where: { id: data.createdBy, deletedAt: null } });
    if (!createdByUser) { throw new NotFoundException(`Creator user with ID "${data.createdBy}" not found.`); }

    return this.prisma.task.create({
      data: {
        ...data,
        status: data.status || $Enums.TaskStatus.PENDING,
        priority: data.priority || $Enums.TaskPriority.MEDIUM, // enum varsayılanı
        dueDate: data.dueDate ? new Date(data.dueDate) : undefined,
        completedAt: data.completedAt ? new Date(data.completedAt) : undefined,
      },
    });
  }

  async findAllTasks(
    companyId?: string,
    branchId?: string,
    assignedToId?: string,
    status?: $Enums.TaskStatus,
    priority?: $Enums.TaskPriority,
    startDate?: Date,
    endDate?: Date,
  ) {
    return this.prisma.task.findMany({
      where: {
        companyId: companyId || undefined,
        branchId: branchId || undefined,
        assignedToId: assignedToId || undefined,
        status: status || undefined,
        priority: priority || undefined,
        createdAt: { // Oluşturulma tarihi
          gte: startDate || undefined,
          lte: endDate ? new Date(endDate.getTime() + (24 * 60 * 60 * 1000) - 1) : undefined,
        },
      },
      include: {
        company: { select: { id: true, name: true } },
        branch: { select: { id: true, name: true } },
        assignedTo: { select: { id: true, firstName: true, lastName: true, username: true } },
        createdByUser: { select: { id: true, firstName: true, lastName: true, username: true } },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOneTask(id: string) {
    const task = await this.prisma.task.findUnique({
      where: { id },
      include: {
        company: { select: { id: true, name: true } },
        branch: { select: { id: true, name: true } },
        assignedTo: { select: { id: true, firstName: true, lastName: true, username: true } },
        createdByUser: { select: { id: true, firstName: true, lastName: true, username: true } },
      },
    });
    if (!task) {
      throw new NotFoundException(`Task with ID "${id}" not found.`);
    }
    return task;
  }

  async updateTask(id: string, data: UpdateTaskDto) {
    const existingTask = await this.findOneTask(id);

    // Tamamlanmış veya iptal edilmiş görevler güncellenemez
    if (existingTask.status === $Enums.TaskStatus.COMPLETED || existingTask.status === $Enums.TaskStatus.CANCELLED) {
        throw new BadRequestException(`Cannot update a task with status "${existingTask.status}".`);
    }

    // İlişkili varlıkların güncelliğini kontrol et
    if (data.branchId && data.branchId !== existingTask.branchId) {
        const branchExists = await this.prisma.branch.findUnique({ where: { id: data.branchId, deletedAt: null } });
        if (!branchExists) { throw new NotFoundException(`Branch with ID "${data.branchId}" not found.`); }
    }
    if (data.assignedToId && data.assignedToId !== existingTask.assignedToId) {
        const assignedToUser = await this.prisma.user.findUnique({ where: { id: data.assignedToId, deletedAt: null } });
        if (!assignedToUser) { throw new NotFoundException(`Assigned user with ID "${data.assignedToId}" not found.`); }
    }
    // createdBy güncellenemez, değiştirilirse ForbiddenException fırlatır.

    // completedAt set edildiyse, status'ü COMPLETED yap (iş kuralı)
    if (data.completedAt && data.status !== $Enums.TaskStatus.COMPLETED) {
        data.status = $Enums.TaskStatus.COMPLETED;
    }
    // Status COMPLETED yapıldıysa ama completedAt set edilmediyse, şimdi set et
    if (data.status === $Enums.TaskStatus.COMPLETED && !data.completedAt) {
        data.completedAt = new Date().toISOString();
    }


    try {
      return await this.prisma.task.update({
        where: { id },
        data: {
            ...data,
            dueDate: data.dueDate ? new Date(data.dueDate) : undefined,
            completedAt: data.completedAt ? new Date(data.completedAt) : undefined,
        },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Task with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removeTask(id: string) {
    // Görevler genellikle fiziksel olarak silinmez, "CANCELLED" veya "ON_HOLD" olarak işaretlenir.
    // Şemada deletedAt alanı yok. Bu metot görevin durumunu "CANCELLED" olarak güncelleyecek.
    try {
      return await this.prisma.task.update({
        where: { id },
        data: { status: $Enums.TaskStatus.CANCELLED, completedAt: new Date() }, // Tamamlanma zamanını da set edebiliriz
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Task with ID "${id}" not found.`);
      }
      throw error;
    }
  }
}
