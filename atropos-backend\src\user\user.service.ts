// src/user/user.service.ts
import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import * as bcrypt from 'bcryptjs'; // bcrypt'i import et
import { User } from '../../generated/prisma'; // User modelini import et

@Injectable()
export class UserService {
  constructor(private prisma: PrismaService) {}

  async findByUsername(username: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { username },
    });
  }

  async createUser(data: CreateUserDto) {
    // Kullanıcı adı benzersiz mi kontrol et
    const existingUser = await this.prisma.user.findUnique({
      where: { username: data.username },
    });
    if (existingUser) {
      throw new ConflictException(`Username "${data.username}" already taken.`);
    }

    // Şifreyi hashle
    const hashedPassword = await bcrypt.hash(data.password, 10); // 10 salt rounds

    // PIN varsa hashle
    let hashedPin: string | undefined;
    if (data.pin) {
      hashedPin = await bcrypt.hash(data.pin, 10);
    }

    return this.prisma.user.create({
      data: {
        ...data,
        password: hashedPassword,
        pin: hashedPin,
        // permissions alanı doğrudan JSON olarak kaydedilebilir
        // createdAt, updatedAt, deletedAt Prisma tarafından yönetiliyor
      },
    });
  }

  async findAllUsers(companyId?: string, branchId?: string) {
    return this.prisma.user.findMany({
      where: {
        companyId: companyId || undefined,
        branchId: branchId || undefined,
        deletedAt: null, // Soft delete kontrolü
      },
      // İhtiyaç olursa belirli alanları dahil edebilirsin:
      // include: { company: true, branch: true },
      select: { // Şifre ve diğer hassas bilgileri döndürmemek için
        id: true,
        companyId: true,
        branchId: true,
        username: true,
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
        avatar: true,
        role: true,
        permissions: true,
        employeeCode: true,
        hireDate: true,
        birthDate: true,
        nationalId: true,
        vehicleType: true,
        vehiclePlate: true,
        active: true,
        lastLoginAt: true,
        failedLoginCount: true,
        lockedUntil: true,
        version: true,
        createdAt: true,
        updatedAt: true,
        deletedAt: true,
        // company: { select: { name: true } }, // İlişkili company adını seç
        // branch: { select: { name: true } } // İlişkili branch adını seç
      }
    });
  }

  async findOneUser(id: string) {
    const user = await this.prisma.user.findUnique({
      where: { id, deletedAt: null },
      select: { // Şifre ve diğer hassas bilgileri döndürmemek için
        id: true,
        companyId: true,
        branchId: true,
        username: true,
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
        avatar: true,
        role: true,
        permissions: true,
        employeeCode: true,
        hireDate: true,
        birthDate: true,
        nationalId: true,
        vehicleType: true,
        vehiclePlate: true,
        active: true,
        lastLoginAt: true,
        failedLoginCount: true,
        lockedUntil: true,
        version: true,
        createdAt: true,
        updatedAt: true,
        deletedAt: true,
      }
    });
    if (!user) {
      throw new NotFoundException(`User with ID "${id}" not found.`);
    }
    return user;
  }

  async updateUser(id: string, data: UpdateUserDto) {
    // Şifre güncelleniyorsa hashle
    if (data.password) {
      data.password = await bcrypt.hash(data.password, 10);
    }
    // PIN güncelleniyorsa hashle
    if (data.pin) {
      data.pin = await bcrypt.hash(data.pin, 10);
    }
    
    // Kullanıcı adı güncelleniyorsa ve başka bir kullanıcı tarafından kullanılıyorsa kontrol et
    if (data.username) {
        const existingUserWithUsername = await this.prisma.user.findUnique({
            where: { username: data.username },
        });
        if (existingUserWithUsername && existingUserWithUsername.id !== id) {
            throw new ConflictException(`Username "${data.username}" already taken by another user.`);
        }
    }

    try {
      return await this.prisma.user.update({
        where: { id, deletedAt: null },
        data,
        select: { // Şifre ve diğer hassas bilgileri döndürmemek için
            id: true,
            companyId: true,
            branchId: true,
            username: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
            avatar: true,
            role: true,
            permissions: true,
            employeeCode: true,
            hireDate: true,
            birthDate: true,
            nationalId: true,
            vehicleType: true,
            vehiclePlate: true,
            active: true,
            lastLoginAt: true,
            failedLoginCount: true,
            lockedUntil: true,
            version: true,
            createdAt: true,
            updatedAt: true,
            deletedAt: true,
        }
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`User with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removeUser(id: string) {
    // Soft delete uygulaması
    try {
      return await this.prisma.user.update({
        where: { id, deletedAt: null },
        data: { deletedAt: new Date(), active: false }, // Soft delete ve pasif hale getirme
        select: { id: true, deletedAt: true, active: true } // Sadece silme ile ilgili bilgileri dön
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`User with ID "${id}" not found or already deleted.`);
      }
      throw error;
    }
  }
}
