// src/reservation/reservation.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { ReservationService } from './reservation.service';
import { CreateReservationDto } from './dto/create-reservation.dto';
import { UpdateReservationDto } from './dto/update-reservation.dto';
import { ReservationStatus } from '../../generated/prisma';
import { ParseOptionalDatePipe } from '../common/pipes/parse-optional-date.pipe'; // Tarih <PERSON>'ı
import { ParseOptionalEnumPipe } from '../common/pipes/parse-optional-enum.pipe'; // Enum Pipe'ı

@Controller('reservation')
export class ReservationController {
  constructor(private readonly reservationService: ReservationService) {}

  @Post() // POST /reservation
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createReservationDto: CreateReservationDto) {
    return this.reservationService.createReservation(createReservationDto);
  }

  @Get() // GET /reservation?branchId=...&customerId=...&status=...&startDate=...&endDate=...
  findAll(
    @Query('branchId') branchId?: string,
    @Query('customerId') customerId?: string,
    @Query('status', new ParseOptionalEnumPipe(ReservationStatus)) status?: ReservationStatus,
    @Query('startDate', ParseOptionalDatePipe) startDate?: Date,
    @Query('endDate', ParseOptionalDatePipe) endDate?: Date,
  ) {
    return this.reservationService.findAllReservations(
      branchId,
      customerId,
      status,
      startDate,
      endDate,
    );
  }

  @Get(':id') // GET /reservation/:id
  findOne(@Param('id') id: string) {
    return this.reservationService.findOneReservation(id);
  }

  @Patch(':id') // PATCH /reservation/:id
  update(@Param('id') id: string, @Body() updateReservationDto: UpdateReservationDto) {
    return this.reservationService.updateReservation(id, updateReservationDto);
  }

  @Delete(':id') // DELETE /reservation/:id (Durumunu değiştir)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.reservationService.removeReservation(id);
  }
}
