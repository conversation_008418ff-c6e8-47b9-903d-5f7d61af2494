// src/daily-report/daily-report.module.ts
import { Modu<PERSON> } from '@nestjs/common';
import { DailyReportService } from './daily-report.service';
import { DailyReportController } from './daily-report.controller';
import { PrismaModule } from '../prisma/prisma.module';
// DailyReportService, Order, Payment, CashMovement servislerine doğrudan bağımlı değil
// ancak onların verilerini çekmek için PrismaService'i kullanıyor.
// Bu nedenle burada doğrudan OrderModule, PaymentModule vb. import etmeye gerek yok.

@Module({
  imports: [PrismaModule],
  controllers: [DailyReportController],
  providers: [DailyReportService],
  exports: [DailyReportService],
})
export class DailyReportModule {}
