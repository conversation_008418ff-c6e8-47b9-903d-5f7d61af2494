// src/payment-method/payment-method.service.ts
import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreatePaymentMethodDto } from './dto/create-payment-method.dto';
import { UpdatePaymentMethodDto } from './dto/update-payment-method.dto';

@Injectable()
export class PaymentMethodService {
  constructor(private prisma: PrismaService) {}

  async createPaymentMethod(data: CreatePaymentMethodDto) {
    // Şirket mevcut mu kontrol et
    const companyExists = await this.prisma.company.findUnique({
      where: { id: data.companyId, deletedAt: null },
    });
    if (!companyExists) {
      throw new NotFoundException(`Company with ID "${data.companyId}" not found.`);
    }

    // Aynı şirket içinde aynı kodda ödeme yöntemi var mı kontrol et
    const existingPaymentMethod = await this.prisma.paymentMethod.findUnique({
      where: {
        companyId_code: {
          companyId: data.companyId,
          code: data.code,
        },
      },
    });
    if (existingPaymentMethod) {
      throw new ConflictException(`Payment method with code "${data.code}" already exists for this company.`);
    }

    return this.prisma.paymentMethod.create({
      data: {
        ...data,
        commissionRate: data.commissionRate !== undefined ? parseFloat(data.commissionRate.toFixed(2)) : undefined,
        minAmount: data.minAmount !== undefined ? parseFloat(data.minAmount.toFixed(2)) : undefined,
        maxAmount: data.maxAmount !== undefined ? parseFloat(data.maxAmount.toFixed(2)) : undefined,
      },
    });
  }

  async findAllPaymentMethods(companyId?: string) {
    return this.prisma.paymentMethod.findMany({
      where: { companyId: companyId || undefined, deletedAt: null },
      include: { company: { select: { id: true, name: true } } },
      orderBy: { displayOrder: 'asc' },
    });
  }

  async findOnePaymentMethod(id: string) {
    const paymentMethod = await this.prisma.paymentMethod.findUnique({
      where: { id, deletedAt: null },
      include: { company: { select: { id: true, name: true } } },
    });
    if (!paymentMethod) {
      throw new NotFoundException(`Payment method with ID "${id}" not found.`);
    }
    return paymentMethod;
  }

  async updatePaymentMethod(id: string, data: UpdatePaymentMethodDto) {
    // Eğer companyId güncelleniyorsa, yeni şirketin mevcut olduğunu doğrula
    if (data.companyId) {
        const companyExists = await this.prisma.company.findUnique({
            where: { id: data.companyId, deletedAt: null },
        });
        if (!companyExists) {
            throw new NotFoundException(`Company with ID "${data.companyId}" not found.`);
        }
    }

    // Aynı şirket içinde aynı kodda başka bir ödeme yöntemiyle çakışma var mı kontrol et
    if (data.code) {
        const currentPaymentMethod = await this.findOnePaymentMethod(id);
        const existingPaymentMethod = await this.prisma.paymentMethod.findUnique({
            where: {
                companyId_code: {
                    companyId: data.companyId || currentPaymentMethod.companyId,
                    code: data.code,
                },
            },
        });
        if (existingPaymentMethod && existingPaymentMethod.id !== id) {
            throw new ConflictException(`Payment method with code "${data.code}" already exists for this company.`);
        }
    }

    try {
      return await this.prisma.paymentMethod.update({
        where: { id, deletedAt: null },
        data: {
            ...data,
            commissionRate: data.commissionRate !== undefined ? parseFloat(data.commissionRate.toFixed(2)) : undefined,
            minAmount: data.minAmount !== undefined ? parseFloat(data.minAmount.toFixed(2)) : undefined,
            maxAmount: data.maxAmount !== undefined ? parseFloat(data.maxAmount.toFixed(2)) : undefined,
        },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Payment method with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removePaymentMethod(id: string) {
    // Bu ödeme yöntemine bağlı ödeme var mı kontrol et
    const paymentsCount = await this.prisma.payment.count({
        where: { paymentMethodId: id, deletedAt: null }
    });

    if (paymentsCount > 0) {
        throw new ConflictException(`Payment method with ID "${id}" cannot be deleted because it has ${paymentsCount} associated payments.`);
    }

    // Soft delete uygulaması
    try {
      return await this.prisma.paymentMethod.update({
        where: { id, deletedAt: null },
        data: { deletedAt: new Date(), active: false }, // Soft delete ve pasif hale getir
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Payment method with ID "${id}" not found or already deleted.`);
      }
      throw error;
    }
  }
}
