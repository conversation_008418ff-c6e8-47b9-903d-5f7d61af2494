// src/audit-log/audit-log.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { AuditLogService } from './audit-log.service';
import { CreateAuditLogDto } from './dto/create-audit-log.dto';
import { UpdateAuditLogDto } from './dto/update-audit-log.dto';
import { ParseOptionalDatePipe } from '../common/pipes/parse-optional-date.pipe'; // Tarih <PERSON>'ı

@Controller('audit-log')
export class AuditLogController {
  constructor(private readonly auditLogService: AuditLogService) {}

  @Post() // POST /audit-log
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createAuditLogDto: CreateAuditLogDto) {
    return this.auditLogService.createAuditLog(createAuditLogDto);
  }

  @Get() // GET /audit-log?userId=...&action=...&entityType=...&entityId=...&startDate=...&endDate=...
  findAll(
    @Query('userId') userId?: string,
    @Query('action') action?: string,
    @Query('entityType') entityType?: string,
    @Query('entityId') entityId?: string,
    @Query('startDate', ParseOptionalDatePipe) startDate?: Date,
    @Query('endDate', ParseOptionalDatePipe) endDate?: Date,
  ) {
    return this.auditLogService.findAllAuditLogs(
      userId,
      action,
      entityType,
      entityId,
      startDate,
      endDate,
    );
  }

  @Get(':id') // GET /audit-log/:id
  findOne(@Param('id') id: string) {
    return this.auditLogService.findOneAuditLog(id);
  }

  @Patch(':id') // PATCH /audit-log/:id (Forbidden olmalı)
  update(@Param('id') id: string, @Body() updateAuditLogDto: UpdateAuditLogDto) {
    return this.auditLogService.updateAuditLog(id, updateAuditLogDto);
  }

  @Delete(':id') // DELETE /audit-log/:id (Forbidden olmalı)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.auditLogService.removeAuditLog(id);
  }
}
