# RFC: Group `@fluentui/react-components` Packages to Common Folder

---

Contributors: Fluent UI React Build team, @TristanWatanabe

## Summary

This RFC is a follow-up to the [Repo Folder Organization](https://github.com/JustSlone/fluentui/blob/9e46742b4383af09e58365c0deb7bc70a3297261/rfcs/build-system/05-folder-organization.md) RFC which focuses on the contents of a new common folder for `@fluentui/react-components` packages to live in.

## Background

In prep for the stable release of `@fluentui/react-components`, our repo will start to gain more visitors and contributors who will seek `react-components` related source code and information. Having a common folder where all `@fluentui/react-components` components are located will help make this process straightforward.

## Problem statement

As it currently stands, finding `@fluentui/react-components` related components is not straightforward as they currently live alongside `@fluentui/react`, `@fluentui/northstar` and `@fluentui/web-components` components. Simply searching for the `Button` component will yield a plethora of results spanning all 4 libraries within the monorepo. This can lead to confusion amongst contributors who visit our repo.

## Detailed Design or Proposal

1. Within the packages folder, we should create a new subfolder called `react-components` and move all `@fluentui/react-components` packages to it.

   - Packages to move include `keyboard-keys`, `priority-overflow`, `react-accordion`, `react-aria`, `react-avatar`, `react-badge`, `react-button`, `react-card`, `react-checkbox`, `react-combobox`, `react-conformance-griffel`, `react-context-selector`, `react-dialog`, `react-divider`, `react-image`, `react-input`, `react-label`, `react-link`, `react-list`, `react-menu`, `react-popover`, `react-portal`, `react-positioning`, `react-priority-overflow`, `react-provider`, `react-radio`, `react-select`, `react-shared-contexts`, `react-slider`, `react-spinbutton`, `react-spinner`, `react-storybook`, `react-storybook-addon`, `react-switch`, `react-tabs`, `react-tabster`, `react-text`, `react-textarea`, `react-theme`, `react-toolbar`, `react-tooltip`, `react-utilities`, and the `react-components` package itself.

   - Create nx migration generator to perform actual move of packages to the new `react-components` folder.

   - Update `migrate-converged-pkg` generator to account for path change.

2. Ensure that future `@fluentui/react-components` components that are created and bootstrapped are added to the `react-components` subfolder.

   - Update the `create-package` script to add newly created component package to the `packages/react-components` directory.

### Pros and Cons

Pros:

- Easier for contributors to find `@fluentui/react-components` related packages.

Cons:

- Large file moves cause merge conflicts.
