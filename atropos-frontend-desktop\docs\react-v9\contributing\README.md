# Contributing to Fluent UI React v9

This directory contains documentation on best practices and guidelines for contributing to Fluent UI React v9. These resources are designed to help developers understand the development workflow, component implementation process, coding standards, and other important aspects of contributing to the project.

## Development Setup & Workflow

- [Development Environment Setup](./dev-env.md) - How to set up your development environment for contributing to Fluent UI v9.
- [Development Workflow](./dev-workflow.md) - Learn about making changes, creating branches, building, and submitting pull requests.
- [Common Development Snags](./common-dev-snags.md) - Solutions to common issues encountered during development.
- [Command Cheat Sheet](./command-cheat-sheet.md) - Quick reference for frequently used commands.

## Component Development

- [Component Implementation Guide](./component-implementation-guide.md) - Comprehensive guide for implementing new components.
- [New Components](./new-components.md) - Process for adding new components to the library.
- [Implementation Best Practices](./implementation-best-practices.md) - Guidelines for component implementation.

## Testing

- [Testing with Jest](./testing-with-jest.md) - How to write and run unit tests using Jest.
- [E2E Testing with Cypress](./e2e-testing-with-cypress.md) - End-to-end testing using Cypress.
- [Performance Testing](./perf-testing.md) - How to test and optimize component performance.

## Accessibility

- [Accessibility Review Checklist](./accessibility-review-checklist.md) - Comprehensive checklist for accessibility reviews.
- [Accessibility Troubleshooting](./accessibility-troubleshooting.md) - Solving common accessibility issues.

## API & Documentation

- [API Extractor](./api-extractor.md) - Working with API Extractor to document and validate component APIs.
- [Coding Style](./coding-style.md) - Coding standards and style guidelines.

## Process & Release

- [Release Cycle](./release-cycle.md) - Understanding the release process for v9 packages.
- [RFC Process](./rfc-process.md) - Process for proposing new features or significant changes.
- [Contributor License Agreement](./cla.md) - Information about the Contributor License Agreement (CLA).

## Team Collaboration

- [Keeping Up with Review Requests](./keeping-up-with-review-requests.md) - Best practices for handling code reviews.
- [Conducting Meetings Style Guide](./conducting-meetings-style-guide.md) - Guidelines for effective meetings.

## Advanced Topics

- [Unstable Hooks](./unstable-hooks.md) - Working with experimental React hooks.
- [Using Local Unpublished Version](./using-local-unpublished-version-of-the-lib-with-a-local-React-app.md) - How to use a local version of Fluent UI with a React app.

## Additional Resources

- [Patterns](./patterns/) - Design patterns used in Fluent UI v9.
- [RFCs](./rfcs/) - Request for Comments documents proposing significant changes.
