// src/table-area/table-area.service.ts
import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateTableAreaDto } from './dto/create-table-area.dto';
import { UpdateTableAreaDto } from './dto/update-table-area.dto';

@Injectable()
export class TableAreaService {
  constructor(private prisma: PrismaService) {}

  async createTableArea(data: CreateTableAreaDto) {
    // Şube mevcut mu kontrol et
    const branchExists = await this.prisma.branch.findUnique({
      where: { id: data.branchId, deletedAt: null },
    });
    if (!branchExists) {
      throw new NotFoundException(`Branch with ID "${data.branchId}" not found.`);
    }

    // Aynı şube içinde aynı isimde masa alanı var mı kontrol et
    const existingTableArea = await this.prisma.tableArea.findFirst({
      where: {
        branchId: data.branchId,
        name: data.name,
        deletedAt: null,
      },
    });
    if (existingTableArea) {
      throw new ConflictException(`Table area with name "${data.name}" already exists for this branch.`);
    }

    return this.prisma.tableArea.create({ data });
  }

  async findAllTableAreas(branchId?: string) {
    return this.prisma.tableArea.findMany({
      where: { branchId: branchId || undefined, deletedAt: null },
      include: { branch: { select: { id: true, name: true } } },
      orderBy: { displayOrder: 'asc' },
    });
  }

  async findOneTableArea(id: string) {
    const tableArea = await this.prisma.tableArea.findUnique({
      where: { id, deletedAt: null },
      include: { branch: { select: { id: true, name: true } } },
    });
    if (!tableArea) {
      throw new NotFoundException(`Table area with ID "${id}" not found.`);
    }
    return tableArea;
  }

  async updateTableArea(id: string, data: UpdateTableAreaDto) {
    // Eğer branchId güncelleniyorsa, yeni şubenin mevcut olduğunu doğrula
    if (data.branchId) {
        const branchExists = await this.prisma.branch.findUnique({
            where: { id: data.branchId, deletedAt: null },
        });
        if (!branchExists) {
            throw new NotFoundException(`Branch with ID "${data.branchId}" not found.`);
        }
    }

    // Aynı şube içinde aynı isimde başka bir masa alanıyla çakışma var mı kontrol et
    if (data.name) {
        const currentTableArea = await this.findOneTableArea(id);
        const existingTableArea = await this.prisma.tableArea.findFirst({
            where: {
                branchId: data.branchId || currentTableArea.branchId, // Yeni veya mevcut branchId
                name: data.name,
                id: { not: id }, // Kendi ID'si hariç
                deletedAt: null
            }
        });
        if (existingTableArea) {
            throw new ConflictException(`Table area with name "${data.name}" already exists for this branch.`);
        }
    }

    try {
      return await this.prisma.tableArea.update({
        where: { id, deletedAt: null },
        data,
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Table area with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removeTableArea(id: string) {
    // Bu masa alanına bağlı masa var mı kontrol et
    const tablesCount = await this.prisma.table.count({
        where: { areaId: id, deletedAt: null }
    });

    if (tablesCount > 0) {
        throw new ConflictException(`Table area with ID "${id}" cannot be deleted because it has ${tablesCount} active tables.`);
    }

    // Soft delete uygulaması
    try {
      return await this.prisma.tableArea.update({
        where: { id, deletedAt: null },
        data: { deletedAt: new Date(), active: false }, // Soft delete ve pasif hale getirme
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Table area with ID "${id}" not found or already deleted.`);
      }
      throw error;
    }
  }
}
