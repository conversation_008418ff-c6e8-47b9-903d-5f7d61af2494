// src/company/dto/create-company.dto.ts
// npm install class-validator class-transformer --save
import { IsString, IsNotEmpty, IsOptional, IsEmail, IsBoolean, IsInt, IsArray, IsDecimal } from 'class-validator';

export class CreateCompanyDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  taxNumber: string;

  @IsString()
  @IsNotEmpty()
  taxOffice: string;

  @IsString()
  @IsNotEmpty()
  address: string;

  @IsString()
  @IsNotEmpty()
  phone: string;

  @IsString()
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsString()
  @IsOptional()
  logo?: string;

  @IsString()
  @IsOptional()
  website?: string;

  @IsString()
  @IsOptional()
  eArchiveUsername?: string;

  @IsString()
  @IsOptional()
  eArchivePassword?: string;

  @IsString()
  @IsOptional()
  eInvoiceUsername?: string;

  @IsString()
  @IsOptional()
  eInvoicePassword?: string;

  @IsString()
  @IsOptional()
  smsProvider?: string;

  @IsString()
  @IsOptional()
  smsApiKey?: string;

  @IsString()
  @IsOptional()
  smsApiSecret?: string;

  @IsString()
  @IsOptional()
  smsSenderName?: string;
}
