// src/online-platform/online-platform.service.ts
import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateOnlinePlatformDto } from './dto/create-online-platform.dto';
import { UpdateOnlinePlatformDto } from './dto/update-online-platform.dto';

@Injectable()
export class OnlinePlatformService {
  constructor(private prisma: PrismaService) {}

  async createOnlinePlatform(data: CreateOnlinePlatformDto) {
    // Şirket mevcut mu kontrol et
    const companyExists = await this.prisma.company.findUnique({
      where: { id: data.companyId, deletedAt: null },
    });
    if (!companyExists) {
      throw new NotFoundException(`Company with ID "${data.companyId}" not found.`);
    }

    // Aynı şirket içinde aynı kodda online platform var mı kontrol et
    const existingPlatform = await this.prisma.onlinePlatform.findUnique({
      where: {
        companyId_code: {
          companyId: data.companyId,
          code: data.code,
        },
      },
    });
    if (existingPlatform) {
      throw new ConflictException(`Online platform with code "${data.code}" already exists for this company.`);
    }

    return this.prisma.onlinePlatform.create({
      data: {
        ...data,
        commissionRate: data.commissionRate !== undefined ? parseFloat(data.commissionRate.toFixed(2)) : undefined,
      },
    });
  }

  async findAllOnlinePlatforms(companyId?: string) {
    return this.prisma.onlinePlatform.findMany({
      where: { companyId: companyId || undefined }, // soft delete alanı yok
      include: { company: { select: { id: true, name: true } } },
      orderBy: { name: 'asc' },
    });
  }

  async findOneOnlinePlatform(id: string) {
    const platform = await this.prisma.onlinePlatform.findUnique({
      where: { id }, // soft delete alanı yok
      include: { company: { select: { id: true, name: true } } },
    });
    if (!platform) {
      throw new NotFoundException(`Online platform with ID "${id}" not found.`);
    }
    return platform;
  }

  async updateOnlinePlatform(id: string, data: UpdateOnlinePlatformDto) {
    // Eğer companyId güncelleniyorsa, yeni şirketin mevcut olduğunu doğrula
    if (data.companyId) {
        const companyExists = await this.prisma.company.findUnique({
            where: { id: data.companyId, deletedAt: null },
        });
        if (!companyExists) {
            throw new NotFoundException(`Company with ID "${data.companyId}" not found.`);
        }
    }

    // Aynı şirket içinde aynı kodda başka bir platformla çakışma var mı kontrol et
    if (data.code) {
        const currentPlatform = await this.findOneOnlinePlatform(id);
        const existingPlatform = await this.prisma.onlinePlatform.findUnique({
            where: {
                companyId_code: {
                    companyId: data.companyId || currentPlatform.companyId,
                    code: data.code,
                },
            },
        });
        if (existingPlatform && existingPlatform.id !== id) {
            throw new ConflictException(`Online platform with code "${data.code}" already exists for this company.`);
        }
    }

    try {
      return await this.prisma.onlinePlatform.update({
        where: { id }, // soft delete alanı yok
        data: {
            ...data,
            commissionRate: data.commissionRate !== undefined ? parseFloat(data.commissionRate.toFixed(2)) : undefined,
        },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Online platform with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removeOnlinePlatform(id: string) {
    // Bu platforma bağlı online sipariş veya ürün eşleşmesi var mı kontrol et
    const onlineOrdersCount = await this.prisma.onlineOrder.count({
        where: { platformId: id }
    });
    if (onlineOrdersCount > 0) {
        throw new ConflictException(`Online platform with ID "${id}" cannot be deleted because it has ${onlineOrdersCount} associated online orders.`);
    }

    const productMappingsCount = await this.prisma.onlineProductMapping.count({
        where: { platformId: id }
    });
    if (productMappingsCount > 0) {
        throw new ConflictException(`Online platform with ID "${id}" cannot be deleted because it has ${productMappingsCount} associated product mappings.`);
    }

    // OnlinePlatform modelinde soft delete alanı yok, bu nedenle fiziksel silme yapıyoruz.
    try {
      return await this.prisma.onlinePlatform.delete({
        where: { id },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Online platform with ID "${id}" not found.`);
      }
      throw error;
    }
  }
}
