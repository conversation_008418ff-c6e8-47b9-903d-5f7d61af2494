// src/auth/jwt.strategy.ts
import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config'; // Ortam değişkenlerini okumak için
import { PrismaService } from '../prisma/prisma.service'; // Kullanıcıyı doğrulamak için

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private configService: ConfigService, // Ortam değişkenleri için
    private prisma: PrismaService, // Kullanıcıyı doğrulamak için
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(), // Token'ı Bearer başlığından al
      ignoreExpiration: false, // Token süresinin dolup dolmadığını kontrol et
      secretOrKey: configService.get<string>('JWT_SECRET') || 'defaultSecret', // .env'den SECRET key'i al
    });
  }

  async validate(payload: any) {
    // payload: { username: user.username, sub: user.id, role: user.role }
    // Veritabanından kullanıcıyı doğrula
    const user = await this.prisma.user.findUnique({
      where: { id: payload.sub, username: payload.username, deletedAt: null },
      // Ayrıca refresh token varlığı, session validasyonu gibi ek güvenlik kontrolleri eklenebilir
    });

    if (!user) {
      throw new UnauthorizedException();
    }
    // Kullanıcının hassas bilgilerini döndürme
    const { password, refreshToken, ...result } = user;
    return result; // İsteğe eklenecek kullanıcı objesi (request.user)
  }
}
