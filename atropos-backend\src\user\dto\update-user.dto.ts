// src/user/dto/update-user.dto.ts
import { PartialType } from '@nestjs/mapped-types';
import { CreateUserDto } from './create-user.dto';
import { IsString, IsOptional, MinLength, IsDate, IsInt } from 'class-validator'; // <PERSON><PERSON> validatorları
import { Type } from 'class-transformer';

export class UpdateUserDto extends PartialType(CreateUserDto) {
    @IsString()
    @IsOptional()
    @MinLength(8) // Şifre güncellenirken minimum uzunluk
    password?: string; // Şifrenin güncellenebilir olması ama opsiyonel olması

    @IsDate()
    @IsOptional()
    @Type(() => Date)
    lastLoginAt?: Date; // Son giriş zamanı

    @IsInt()
    @IsOptional()
    failedLoginCount?: number; // Başarısız giriş sayısı
}
