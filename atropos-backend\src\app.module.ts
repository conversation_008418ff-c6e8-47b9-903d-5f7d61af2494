// src/app.module.ts
import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule } from '@nestjs/config'; // ConfigModule'ü import et
import { PrismaModule } from './prisma/prisma.module';
import { CompanyModule } from './company/company.module';
import { BranchModule } from './branch/branch.module'; // Bu satırı ekle
import { UserModule } from './user/user.module'; // Bu satırı ekle
import { CategoryModule } from './category/category.module'; // Bu satırı ekle
import { TaxModule } from './tax/tax.module'; // Bu satırı ekle
import { ProductModule } from './product/product.module'; // Bu satırı ekle
import { TableAreaModule } from './table-area/table-area.module'; // Bu satırı ekle
import { TableModule } from './table/table.module'; // Bu satırı ekle
import { CustomerModule } from './customer/customer.module'; // Bu satırı ekle
import { OrderModule } from './order/order.module'; // Bu satırı ekle
import { PaymentMethodModule } from './payment-method/payment-method.module'; // Bu satırı ekle
import { PaymentModule } from './payment/payment.module'; // Bu satırı ekle
import { CashMovementModule } from './cash-movement/cash-movement.module'; // Bu satırı ekle
import { DailyReportModule } from './daily-report/daily-report.module'; // Bu satırı ekle
import { InvoiceModule } from './invoice/invoice.module';
import { OnlinePlatformModule } from './online-platform/online-platform.module'; // Bu satırı ekle
import { OnlineProductMappingModule } from './online-product-mapping/online-product-mapping.module'; // Bu satırı ekle
import { OnlineOrderModule } from './online-order/online-order.module'; // Bu satırı ekle
import { InventoryItemModule } from './inventory-item/inventory-item.module'; // Bu satırı ekle
import { RecipeModule } from './recipe/recipe.module'; // Bu satırı ekle
import { StockMovementModule } from './stock-movement/stock-movement.module'; // Bu satırı ekle
import { StockCountModule } from './stock-count/stock-count.module'; // Bu satırı ekle
import { LoyaltyCardModule } from './loyalty-card/loyalty-card.module'; // Bu satırı ekle
import { LoyaltyTransactionModule } from './loyalty-transaction/loyalty-transaction.module'; // Bu satırı ekle
import { ReservationModule } from './reservation/reservation.module'; // Bu satırı ekle
import { CampaignModule } from './campaign/campaign.module'; // Bu satırı ekle
import { PrinterGroupModule } from './printer-group/printer-group.module'; // Bu satırı ekle
import { PrinterModule } from './printer/printer.module'; // Bu satırı ekle
import { NotificationTemplateModule } from './notification-template/notification-template.module'; // Bu satırı ekle
import { NotificationLogModule } from './notification-log/notification-log.module'; // Bu satırı ekle
import { CourierLocationModule } from './courier-location/courier-location.module'; // Bu satırı ekle
import { PriceOverrideModule } from './price-override/price-override.module'; // Bu satırı ekle
import { TableMergeModule } from './table-merge/table-merge.module'; // Bu satırı ekle
import { AuditLogModule } from './audit-log/audit-log.module'; // Bu satırı ekle
import { OrderLogModule } from './order-log/order-log.module'; // Bu satırı ekle
import { TaskModule } from './task/task.module'; // Bu satırı ekle
import { AuthModule } from './auth/auth.module';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }), // .env dosyalarını okumak için ve tüm uygulamada geçerli kıl
    PrismaModule,
    AuthModule, // AuthModule'ü imports dizisine ekle
    CompanyModule,
    BranchModule,
    UserModule,
    CategoryModule,
    TaxModule,
    ProductModule,
    TableAreaModule,
    TableModule,
    CustomerModule,
    OrderModule,
    PaymentMethodModule,
    PaymentModule,
    CashMovementModule,
    DailyReportModule,
    InvoiceModule,
    OnlinePlatformModule,
    OnlineProductMappingModule,
    OnlineOrderModule,
    InventoryItemModule,
    RecipeModule,
    StockMovementModule,
    StockCountModule,
    LoyaltyCardModule,
    LoyaltyTransactionModule,
    ReservationModule,
    CampaignModule,
    PrinterGroupModule,
    PrinterModule,
    NotificationTemplateModule,
    NotificationLogModule,
    CourierLocationModule,
    PriceOverrideModule,
    TableMergeModule,
    AuditLogModule,
    OrderLogModule,
    TaskModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}