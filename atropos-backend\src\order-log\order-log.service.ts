// src/order-log/order-log.service.ts
import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateOrderLogDto } from './dto/create-order-log.dto';
import { UpdateOrderLogDto } from './dto/update-order-log.dto';

@Injectable()
export class OrderLogService {
  constructor(private prisma: PrismaService) {}

  async createOrderLog(data: CreateOrderLogDto) {
    // Order mevcut mu kontrol et
    const orderExists = await this.prisma.order.findUnique({
      where: { id: data.orderId, deletedAt: null },
    });
    if (!orderExists) {
      throw new NotFoundException(`Order with ID "${data.orderId}" not found for order log.`);
    }

    // userId belirtilmişse kullanıcının varlığın<PERSON> kontrol et
    if (data.userId) {
      const userExists = await this.prisma.user.findUnique({
        where: { id: data.userId, deletedAt: null },
      });
      if (!userExists) {
        throw new NotFoundException(`User with ID "${data.userId}" not found for order log.`);
      }
    }

    return this.prisma.orderLog.create({
      data: {
        ...data,
        timestamp: new Date(), // Otomatik zaman damgası
      },
    });
  }

  async findAllOrderLogs(orderId?: string, userId?: string, action?: string, startDate?: Date, endDate?: Date) {
    return this.prisma.orderLog.findMany({
      where: {
        orderId: orderId || undefined,
        userId: userId || undefined,
        action: action || undefined,
        timestamp: {
          gte: startDate || undefined,
          lte: endDate ? new Date(endDate.getTime() + (24 * 60 * 60 * 1000) - 1) : undefined, // Gün sonunu dahil et
        },
      },
      include: {
        order: { select: { id: true, orderNumber: true, status: true } },
        user: { select: { id: true, username: true, firstName: true, lastName: true } },
      },
      orderBy: { timestamp: 'desc' },
    });
  }

  async findOneOrderLog(id: string) {
    const log = await this.prisma.orderLog.findUnique({
      where: { id },
      include: {
        order: { select: { id: true, orderNumber: true, status: true } },
        user: { select: { id: true, username: true, firstName: true, lastName: true } },
      },
    });
    if (!log) {
      throw new NotFoundException(`Order log with ID "${id}" not found.`);
    }
    return log;
  }

  async updateOrderLog(id: string, data: UpdateOrderLogDto) {
    // Order logları değiştirilemez olmalıdır.
    throw new ForbiddenException('Order logs cannot be updated.');
  }

  async removeOrderLog(id: string) {
    // Order logları fiziksel olarak silinmemelidir, denetim izi bozulur.
    throw new ForbiddenException('Order logs cannot be deleted.');
  }
}
