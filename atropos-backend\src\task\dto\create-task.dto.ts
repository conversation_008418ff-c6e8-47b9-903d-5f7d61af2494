// src/task/dto/create-task.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsDateString,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { UserRole, $Enums } from '../../../generated/prisma'; // Test için UserRole import et
// import { TaskStatus, TaskPriority } from '../../../generated/prisma'; // Enum'ları import et

export class CreateTaskDto {
  @IsString()
  @IsOptional() // null = tüm şubelerle ilgili olabilir
  branchId?: string;

  @IsString()
  @IsNotEmpty()
  companyId: string;

  @IsString()
  @IsNotEmpty()
  title: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  assignedToId?: string; // Görev atanan kullanıcı ID'si

  @IsEnum($Enums.TaskStatus)
  @IsOptional()
  status?: $Enums.TaskStatus; // Varsayılan: PENDING

  @IsEnum($Enums.TaskPriority)
  @IsOptional()
  priority?: $Enums.TaskPriority; // Varsayılan: MEDIUM

  @IsOptional()
  @IsDateString()
  dueDate?: Date;

  @IsOptional()
  @IsDateString()
  completedAt?: Date; // Görevin tamamlanma zamanı

  @IsString()
  @IsNotEmpty()
  createdBy: string; // Görevi oluşturan kullanıcı ID'si
}
