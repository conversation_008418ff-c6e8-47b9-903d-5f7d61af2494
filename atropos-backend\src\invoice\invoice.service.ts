// src/invoice/invoice.service.ts
import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateInvoiceDto } from './dto/create-invoice.dto';
import { UpdateInvoiceDto } from './dto/update-invoice.dto';
// Enum'ları object olarak tanımlayalım
const InvoiceType = {
  RECEIPT: 'RECEIPT',
  INVOICE: 'INVOICE',
  E_ARCHIVE: 'E_ARCHIVE',
  E_INVOICE: 'E_INVOICE',
  PROFORMA: 'PROFORMA',
  RETURN: 'RETURN'
} as const;

const EArchiveStatus = {
  PENDING: 'PENDING',
  SENT: 'SENT',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  CANCELLED: 'CANCELLED'
} as const;

type InvoiceType = typeof InvoiceType[keyof typeof InvoiceType];
type EArchiveStatus = typeof EArchiveStatus[keyof typeof EArchiveStatus];

@Injectable()
export class InvoiceService {
  constructor(private prisma: PrismaService) {}

  // Otomatik fatura numarası oluşturma (YYYY-XXXXXX veya seri+sıra)
  private async generateInvoiceNumber(invoiceType: InvoiceType, serialNo: string): Promise<string> {
    // Şu anki yıl
    const currentYearPrefix = new Date().getFullYear().toString();
    
    // Verilen seri ve yıl için en yüksek sıra numarasını bul
    const latestInvoice = await this.prisma.invoice.findFirst({
        where: { serialNo, sequenceNo: { startsWith: currentYearPrefix } }, // Sadece bu serinin bu yılki faturalarına bak
        orderBy: { createdAt: 'desc' } // En son oluşturulanı al
    });

    let newSequence = 1;
    if (latestInvoice && latestInvoice.sequenceNo) {
        const lastSeqNum = parseInt(latestInvoice.sequenceNo.substring(currentYearPrefix.length)); // Yıl kısmını çıkarıp kalan sıra numarasını al
        newSequence = lastSeqNum + 1;
    }
    return `${currentYearPrefix}${String(newSequence).padStart(6, '0')}`; // Örn: 2025000001
  }

  async createInvoice(data: CreateInvoiceDto) {
    // Fatura numarası benzersiz mi kontrol et
    const existingInvoiceByNumber = await this.prisma.invoice.findUnique({
      where: {
        serialNo_sequenceNo: {
          serialNo: data.serialNo,
          sequenceNo: data.sequenceNo || '',
        },
      },
    });
    if (existingInvoiceByNumber) {
      throw new ConflictException(`Invoice with serial number "${data.serialNo}" and sequence number "${data.sequenceNo}" already exists.`);
    }

    // orderId varsa, siparişin mevcut olduğunu kontrol et
    let order: any = null;
    if (data.orderId) {
      order = await this.prisma.order.findUnique({
        where: { id: data.orderId, deletedAt: null },
        include: { 
          items: { include: { product: { include: { tax: true } } } },
          customer: true
        },
      });
      if (!order) {
        throw new NotFoundException(`Order with ID "${data.orderId}" not found.`);
      }
      // Siparişin zaten bir faturası var mı kontrol et
      const existingInvoiceForOrder = await this.prisma.invoice.findUnique({
        where: { orderId: data.orderId },
      });
      if (existingInvoiceForOrder) {
        throw new ConflictException(`An invoice already exists for order with ID "${data.orderId}".`);
      }
    }

    // Eğer orderId belirtilmişse tutarları siparişten çek, aksi halde DTO'dan gelenleri kullan
    let subtotal: number;
    let discountAmount: number;
    let taxAmount: number;
    let totalAmount: number;
    let taxDetails: any;

    if (order) {
        subtotal = order.subtotal.toNumber();
        discountAmount = order.discountAmount.toNumber();
        taxAmount = order.taxAmount.toNumber();
        totalAmount = order.totalAmount.toNumber();

        // KDV matrahlarını sipariş kalemlerinden hesapla
        const calculatedTaxDetails: { [taxId: string]: { name: string, rate: number, base: number, amount: number } } = {};
        for (const item of order.items) {
            const itemSubtotal = (item.unitPrice.toNumber() * item.quantity.toNumber()) - (item.discountAmount.toNumber() || (item.discountRate.toNumber() ? (item.unitPrice.toNumber() * item.discountRate.toNumber()) / 100 : 0));
            const taxRate = item.taxRate.toNumber();
            const taxCode = item.product.tax.code;

            if (!calculatedTaxDetails[taxCode]) {
                calculatedTaxDetails[taxCode] = { name: item.product.tax.name, rate: taxRate, base: 0, amount: 0 };
            }
            calculatedTaxDetails[taxCode].base += itemSubtotal;
            calculatedTaxDetails[taxCode].amount += (itemSubtotal * taxRate) / 100;
        }
        taxDetails = calculatedTaxDetails;

        // Müşteri bilgilerini siparişten veya ilişkili müşteriden al
        data.customerName = data.customerName || order.customerName || (order.customer ? order.customer.firstName + ' ' + order.customer.lastName : undefined);
        data.taxNumber = data.taxNumber || order.customer?.taxNumber;
        data.taxOffice = data.taxOffice || order.customer?.taxOffice;
        data.address = data.address || order.customer?.address || order.deliveryAddress;
        data.phone = data.phone || order.customerPhone || order.customer?.phone;
        data.email = data.email || order.customer?.email;

    } else {
        // Eğer orderId yoksa ve manuel fatura ise DTO'dan gelen verileri kullan
        if (data.subtotal === undefined || data.taxAmount === undefined || data.totalAmount === undefined) {
            throw new BadRequestException('For manual invoices, subtotal, taxAmount, and totalAmount must be provided.');
        }
        subtotal = data.subtotal;
        discountAmount = data.discountAmount || 0;
        taxAmount = data.taxAmount;
        totalAmount = data.totalAmount;
        taxDetails = data.taxDetails || {};
    }
    
    // Eğer sequenceNo otomatik oluşturulacaksa
    if (!data.sequenceNo) {
        data.sequenceNo = await this.generateInvoiceNumber(data.invoiceType, data.serialNo);
    }

    const invoice = await this.prisma.invoice.create({
      data: {
        orderId: order?.id,
        invoiceType: data.invoiceType,
        serialNo: data.serialNo,
        sequenceNo: data.sequenceNo,
        customerName: data.customerName,
        customerTaxNo: data.taxNumber,
        customerTaxOffice: data.taxOffice,
        customerAddress: data.address,
        customerPhone: data.phone,
        customerEmail: data.email,
        subtotal: parseFloat(subtotal.toFixed(2)),
        discountAmount: parseFloat(discountAmount.toFixed(2)),
        taxAmount: parseFloat(taxAmount.toFixed(2)),
        totalAmount: parseFloat(totalAmount.toFixed(2)),
        totalAmountText: data.totalAmountText,
        taxDetails: taxDetails,
        eArchiveStatus: data.eArchiveStatus || (data.invoiceType === 'E_ARCHIVE' ? 'PENDING' : undefined), // E-arşiv ise başlangıç durumu
      },
    });

    // Siparişin invoiceId'sini güncelle
    if (order) {
        await this.prisma.order.update({
            where: { id: order.id },
            data: { invoice: { connect: { id: invoice.id } } }, // Order ile Invoice'ı bağla
        });
    }

    return invoice;
  }

  async findAllInvoices(orderId?: string, customerTaxNo?: string, invoiceType?: InvoiceType, startDate?: Date, endDate?: Date) {
    return this.prisma.invoice.findMany({
      where: {
        orderId: orderId || undefined,
        customerTaxNo: customerTaxNo || undefined,
        invoiceType: invoiceType || undefined,
        createdAt: {
          gte: startDate || undefined,
          lte: endDate ? new Date(endDate.getTime() + (24 * 60 * 60 * 1000) - 1) : undefined, // Gün sonu
        },
        deletedAt: null,
        isCancelled: false // İptal edilmemiş faturaları getir
      },
      include: { order: { select: { id: true, orderNumber: true, totalAmount: true } } },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOneInvoice(id: string) {
    const invoice = await this.prisma.invoice.findUnique({
      where: { id, deletedAt: null },
      include: { order: { select: { id: true, orderNumber: true, totalAmount: true } } },
    });
    if (!invoice) {
      throw new NotFoundException(`Invoice with ID "${id}" not found.`);
    }
    return invoice;
  }

  async updateInvoice(id: string, data: UpdateInvoiceDto) {
    const existingInvoice = await this.findOneInvoice(id);

    if (existingInvoice.isCancelled) {
        throw new BadRequestException(`Cannot update a cancelled invoice.`);
    }

    // Eğer fatura numarası güncelleniyorsa unique kontrolü
    if (data.serialNo || data.sequenceNo) {
        const newSerialNo = data.serialNo || existingInvoice.serialNo;
        const newSequenceNo = data.sequenceNo || existingInvoice.sequenceNo;
        const existingWithNewNumber = await this.prisma.invoice.findUnique({
            where: { serialNo_sequenceNo: { serialNo: newSerialNo, sequenceNo: newSequenceNo } },
        });
        if (existingWithNewNumber && existingWithNewNumber.id !== id) {
            throw new ConflictException(`Invoice with serial number "${newSerialNo}" and sequence number "${newSequenceNo}" already exists.`);
        }
    }

    // Faturayı iptal etme işlemi
    if (data.isCancelled && !existingInvoice.isCancelled) {
        // İptal edilen faturaya referans eklenebilir
        data.cancelledInvoiceId = data.cancelledInvoiceId || existingInvoice.id; // Kendini referans gösterebilir
        data.cancelReason = data.cancelReason || 'Not specified';
        data.eArchiveStatus = 'CANCELLED'; // e-arşiv statüsünü de güncelle
    }

    try {
      return await this.prisma.invoice.update({
        where: { id, deletedAt: null },
        data: {
            ...data,
            // Tutarları elle güncelleyeceksek, hassasiyet koruması
            subtotal: data.subtotal !== undefined ? parseFloat(data.subtotal.toFixed(2)) : undefined,
            discountAmount: data.discountAmount !== undefined ? parseFloat(data.discountAmount.toFixed(2)) : undefined,
            taxAmount: data.taxAmount !== undefined ? parseFloat(data.taxAmount.toFixed(2)) : undefined,
            totalAmount: data.totalAmount !== undefined ? parseFloat(data.totalAmount.toFixed(2)) : undefined,
        },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Invoice with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removeInvoice(id: string) {
    // Faturalar genellikle fiziksel olarak silinmez, isCancelled bayrağı ile yönetilir.
    // Şemada deletedAt alanı olduğu için soft delete yapabiliriz, ancak
    // isCancelled daha spesifik bir durumdur.
    // Bu metod sadece soft delete işlevi görecek.
    try {
      return await this.prisma.invoice.update({
        where: { id, deletedAt: null },
        data: { deletedAt: new Date(), isCancelled: true, eArchiveStatus: 'CANCELLED' }, // Soft delete ve iptal et
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Invoice with ID "${id}" not found or already deleted.`);
      }
      throw error;
    }
  }
}
