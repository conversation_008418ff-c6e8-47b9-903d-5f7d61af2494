// src/stock-movement/stock-movement.module.ts
import { Module } from '@nestjs/common';
import { StockMovementService } from './stock-movement.service';
import { StockMovementController } from './stock-movement.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [StockMovementController],
  providers: [StockMovementService],
  exports: [StockMovementService], // StockCount modülü bağımlı olabilir
})
export class StockMovementModule {}
