// src/product/product.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { ProductService } from './product.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';

@Controller('product')
export class ProductController {
  constructor(private readonly productService: ProductService) {}

  @Post() // POST /product
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createProductDto: CreateProductDto) {
    return this.productService.createProduct(createProductDto);
  }

  @Get() // GET /product?companyId=...&categoryId=...
  findAll(
    @Query('companyId') companyId?: string,
    @Query('categoryId') categoryId?: string,
  ) {
    return this.productService.findAllProducts(companyId, categoryId);
  }

  @Get(':id') // GET /product/:id
  findOne(@Param('id') id: string) {
    return this.productService.findOneProduct(id);
  }

  @Patch(':id') // PATCH /product/:id
  update(@Param('id') id: string, @Body() updateProductDto: UpdateProductDto) {
    return this.productService.updateProduct(id, updateProductDto);
  }

  @Delete(':id') // DELETE /product/:id
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.productService.removeProduct(id);
  }
}
