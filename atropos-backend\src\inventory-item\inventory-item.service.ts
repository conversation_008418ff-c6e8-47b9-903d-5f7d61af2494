// src/inventory-item/inventory-item.service.ts
import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateInventoryItemDto } from './dto/create-inventory-item.dto';
import { UpdateInventoryItemDto } from './dto/update-inventory-item.dto';

@Injectable()
export class InventoryItemService {
  constructor(private prisma: PrismaService) {}

  async createInventoryItem(data: CreateInventoryItemDto) {
    // Eğer productId belirtilmişse, ürünün varlığını kontrol et
    if (data.productId) {
      const productExists = await this.prisma.product.findUnique({
        where: { id: data.productId, deletedAt: null },
      });
      if (!productExists) {
        throw new NotFoundException(`Product with ID "${data.productId}" not found.`);
      }
      // Bir ürünün sadece bir InventoryItem'ı olabilir (varsa)
      const existingMappingToProduct = await this.prisma.inventoryItem.findFirst({
        where: { productId: data.productId, deletedAt: null }
      });
      if (existingMappingToProduct) {
        throw new ConflictException(`Inventory item already exists for product with ID "${data.productId}".`);
      }
    }

    // `code` benzersiz mi kontrol et
    const existingItemByCode = await this.prisma.inventoryItem.findUnique({
      where: { code: data.code, deletedAt: null },
    });
    if (existingItemByCode) {
      throw new ConflictException(`Inventory item with code "${data.code}" already exists.`);
    }

    // `barcode` varsa benzersiz mi kontrol et
    if (data.barcode) {
      const existingItemByBarcode = await this.prisma.inventoryItem.findFirst({
        where: { barcode: data.barcode, deletedAt: null },
      });
      if (existingItemByBarcode) {
        throw new ConflictException(`Inventory item with barcode "${data.barcode}" already exists.`);
      }
    }

    // Stok miktarlarını varsayılan olarak ayarla veya DTO'dan gelenleri kullan
    const currentStock = data.currentStock !== undefined ? parseFloat(data.currentStock.toFixed(3)) : 0;
    const reservedStock = data.reservedStock !== undefined ? parseFloat(data.reservedStock.toFixed(3)) : 0;
    const availableStock = currentStock - reservedStock;

    return this.prisma.inventoryItem.create({
      data: {
        ...data,
        currentStock: currentStock,
        reservedStock: reservedStock,
        availableStock: parseFloat(availableStock.toFixed(3)),
        lastCost: data.lastCost !== undefined ? parseFloat(data.lastCost.toFixed(2)) : undefined,
        averageCost: data.averageCost !== undefined ? parseFloat(data.averageCost.toFixed(2)) : undefined,
        criticalLevel: data.criticalLevel !== undefined ? parseFloat(data.criticalLevel.toFixed(3)) : undefined,
        optimalLevel: data.optimalLevel !== undefined ? parseFloat(data.optimalLevel.toFixed(3)) : undefined,
      },
    });
  }

  async findAllInventoryItems(productId?: string, code?: string, supplier?: string) {
    return this.prisma.inventoryItem.findMany({
      where: {
        productId: productId || undefined,
        code: code || undefined,
        supplier: supplier || undefined,
        deletedAt: null,
      },
      include: {
        product: { select: { id: true, name: true, code: true } },
      },
      orderBy: { name: 'asc' },
    });
  }

  async findOneInventoryItem(id: string) {
    const item = await this.prisma.inventoryItem.findUnique({
      where: { id, deletedAt: null },
      include: {
        product: { select: { id: true, name: true, code: true } },
      },
    });
    if (!item) {
      throw new NotFoundException(`Inventory item with ID "${id}" not found.`);
    }
    return item;
  }

  async updateInventoryItem(id: string, data: UpdateInventoryItemDto) {
    const existingItem = await this.findOneInventoryItem(id);

    // Eğer productId güncelleniyorsa ve varlığı kontrol et
    if (data.productId && data.productId !== existingItem.productId) {
      const productExists = await this.prisma.product.findUnique({
        where: { id: data.productId, deletedAt: null },
      });
      if (!productExists) {
        throw new NotFoundException(`Product with ID "${data.productId}" not found.`);
      }
      // Yeni productId'nin başka bir InventoryItem'a bağlı olup olmadığını kontrol et
      const existingMappingToNewProduct = await this.prisma.inventoryItem.findFirst({
        where: { productId: data.productId, deletedAt: null }
      });
      if (existingMappingToNewProduct && existingMappingToNewProduct.id !== id) {
        throw new ConflictException(`Inventory item already exists for product with ID "${data.productId}".`);
      }
    }

    // `code` güncelleniyorsa benzersiz mi kontrol et
    if (data.code && data.code !== existingItem.code) {
      const existingItemByCode = await this.prisma.inventoryItem.findUnique({
        where: { code: data.code, deletedAt: null },
      });
      if (existingItemByCode) {
        throw new ConflictException(`Inventory item with code "${data.code}" already exists.`);
      }
    }

    // `barcode` güncelleniyorsa benzersiz mi kontrol et
    if (data.barcode && data.barcode !== existingItem.barcode) {
      const existingItemByBarcode = await this.prisma.inventoryItem.findFirst({
        where: { barcode: data.barcode, deletedAt: null },
      });
      if (existingItemByBarcode) {
        throw new ConflictException(`Inventory item with barcode "${data.barcode}" already exists.`);
      }
    }

    // Stok miktarları güncelleniyorsa availableStock'u yeniden hesapla
    let updatedAvailableStock: number | undefined;
    if (data.currentStock !== undefined || data.reservedStock !== undefined) {
        const currentStock = data.currentStock !== undefined ? parseFloat(data.currentStock.toFixed(3)) : existingItem.currentStock.toNumber();
        const reservedStock = data.reservedStock !== undefined ? parseFloat(data.reservedStock.toFixed(3)) : existingItem.reservedStock.toNumber();
        updatedAvailableStock = currentStock - reservedStock;
    }

    try {
      return await this.prisma.inventoryItem.update({
        where: { id, deletedAt: null },
        data: {
            ...data,
            currentStock: data.currentStock !== undefined ? parseFloat(data.currentStock.toFixed(3)) : undefined,
            reservedStock: data.reservedStock !== undefined ? parseFloat(data.reservedStock.toFixed(3)) : undefined,
            availableStock: updatedAvailableStock !== undefined ? parseFloat(updatedAvailableStock.toFixed(3)) : undefined,
            lastCost: data.lastCost !== undefined ? parseFloat(data.lastCost.toFixed(2)) : undefined,
            averageCost: data.averageCost !== undefined ? parseFloat(data.averageCost.toFixed(2)) : undefined,
            criticalLevel: data.criticalLevel !== undefined ? parseFloat(data.criticalLevel.toFixed(3)) : undefined,
            optimalLevel: data.optimalLevel !== undefined ? parseFloat(data.optimalLevel.toFixed(3)) : undefined,
        },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Inventory item with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removeInventoryItem(id: string) {
    // Bu envanter öğesine bağlı reçete kalemi veya stok hareketi var mı kontrol et
    const recipeItemsCount = await this.prisma.recipeItem.count({
        where: { inventoryItemId: id }
    });
    if (recipeItemsCount > 0) {
        throw new ConflictException(`Inventory item with ID "${id}" cannot be deleted because it is used in ${recipeItemsCount} recipes.`);
    }

    const stockMovementsCount = await this.prisma.stockMovement.count({
        where: { inventoryItemId: id }
    });
    if (stockMovementsCount > 0) {
        throw new ConflictException(`Inventory item with ID "${id}" cannot be deleted because it has ${stockMovementsCount} associated stock movements.`);
    }
    
    // Soft delete uygulaması
    try {
      return await this.prisma.inventoryItem.update({
        where: { id, deletedAt: null },
        data: { deletedAt: new Date(), active: false }, // Soft delete ve pasif hale getir
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Inventory item with ID "${id}" not found or already deleted.`);
      }
      throw error;
    }
  }
}
