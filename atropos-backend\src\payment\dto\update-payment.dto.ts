// src/payment/dto/update-payment.dto.ts
import { PartialType } from '@nestjs/mapped-types';
import { CreatePaymentDto } from './create-payment.dto';
import {
  IsString,
  IsOptional,
  IsNumber,
  Min,
  IsInt,
  IsEnum,
  IsNotEmpty,
} from 'class-validator';
import { Type } from 'class-transformer';
import { PaymentStatus } from '../../../generated/prisma';

// Refund için ek DTO
export class RefundPaymentDto {
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.01)
  @IsNotEmpty()
  refundAmount: number;

  @IsString()
  @IsNotEmpty()
  refundReason: string;

  @IsString()
  @IsOptional()
  refundedBy?: string; // Refund'u yapan kullanıcı ID'si
}

export class UpdatePaymentDto extends PartialType(CreatePaymentDto) {
  @IsOptional()
  @IsEnum(PaymentStatus)
  status?: PaymentStatus;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  refundAmount?: number; // Sadece bilgi amaçlı veya doğrudan güncelleme için

  @IsString()
  @IsOptional()
  refundReason?: string;

  @IsOptional()
  @Type(() => Date)
  refundedAt?: Date;

  // cashMovementId burada güncellenmez, servis yöneticisi günceller
}
