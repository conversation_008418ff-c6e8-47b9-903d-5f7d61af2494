// src/table-area/table-area.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { TableAreaService } from './table-area.service';
import { CreateTableAreaDto } from './dto/create-table-area.dto';
import { UpdateTableAreaDto } from './dto/update-table-area.dto';

@Controller('table-area')
export class TableAreaController {
  constructor(private readonly tableAreaService: TableAreaService) {}

  @Post() // POST /table-area
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createTableAreaDto: CreateTableAreaDto) {
    return this.tableAreaService.createTableArea(createTableAreaDto);
  }

  @Get() // GET /table-area?branchId=...
  findAll(@Query('branchId') branchId?: string) {
    return this.tableAreaService.findAllTableAreas(branchId);
  }

  @Get(':id') // GET /table-area/:id
  findOne(@Param('id') id: string) {
    return this.tableAreaService.findOneTableArea(id);
  }

  @Patch(':id') // PATCH /table-area/:id
  update(@Param('id') id: string, @Body() updateTableAreaDto: UpdateTableAreaDto) {
    return this.tableAreaService.updateTableArea(id, updateTableAreaDto);
  }

  @Delete(':id') // DELETE /table-area/:id
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.tableAreaService.removeTableArea(id);
  }
}
