// src/loyalty-card/loyalty-card.module.ts
import { <PERSON>du<PERSON> } from '@nestjs/common';
import { LoyaltyCardService } from './loyalty-card.service';
import { LoyaltyCardController } from './loyalty-card.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [LoyaltyCardController],
  providers: [LoyaltyCardService],
  exports: [LoyaltyCardService], // Diğer modüller tarafından kullanılabilir
})
export class LoyaltyCardModule {}
