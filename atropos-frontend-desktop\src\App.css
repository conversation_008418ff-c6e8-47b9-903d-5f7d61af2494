/* Mevcut App.css içeriğini koruyun ve aşağıdaki eklemeleri yapın */

.App-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  /* Windows 11'e benzer arkaplan resmi */
  background-image: url('/windows-11-bg.jpg'); /* public klasöründeki resminizin doğru yolunu kullanın */
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  overflow: hidden; /* Taşmayı engelle */
}

/* Genel fontları Windows 11'e benzer hale getirelim */
body {
  font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif; /* Windows'un sistem fontu */
  margin: 0;
  /* Fluent UI varsayılan tema renklerini sıfırlamak veya belirlemek için */
  /* Bu renkler atroposTheme.ts'den var(--colorNeutralBackground1) gibi CSS değişkenleri olarak gelecek */
  background-color: var(--colorNeutralBackground1);
  color: var(--colorNeutralForeground1);
}

/* LoginScreen ve Dashboard için genel stiller */
.App { /* App.tsx'deki ana div'in sınıfı */
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}
