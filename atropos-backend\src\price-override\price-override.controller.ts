// src/price-override/price-override.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
  ParseBoolPipe,
} from '@nestjs/common';
import { PriceOverrideService } from './price-override.service';
import { CreatePriceOverrideDto } from './dto/create-price-override.dto';
import { UpdatePriceOverrideDto } from './dto/update-price-override.dto';
import { ParseOptionalDatePipe } from '../common/pipes/parse-optional-date.pipe'; // Tarih Pipe'ı

@Controller('price-override')
export class PriceOverrideController {
  constructor(private readonly priceOverrideService: PriceOverrideService) {}

  @Post() // POST /price-override
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createPriceOverrideDto: CreatePriceOverrideDto) {
    return this.priceOverrideService.createPriceOverride(createPriceOverrideDto);
  }

  @Get() // GET /price-override?branchId=...&productId=...&variantId=...&activeOnly=true&date=YYYY-MM-DD
  findAll(
    @Query('branchId') branchId?: string,
    @Query('productId') productId?: string,
    @Query('variantId') variantId?: string,
    @Query('activeOnly', new ParseBoolPipe({ optional: true })) activeOnly?: boolean,
    @Query('date', ParseOptionalDatePipe) date?: Date,
  ) {
    return this.priceOverrideService.findAllPriceOverrides(
      branchId,
      productId,
      variantId,
      activeOnly,
      date,
    );
  }

  @Get(':id') // GET /price-override/:id
  findOne(@Param('id') id: string) {
    return this.priceOverrideService.findOnePriceOverride(id);
  }

  @Patch(':id') // PATCH /price-override/:id
  update(@Param('id') id: string, @Body() updatePriceOverrideDto: UpdatePriceOverrideDto) {
    return this.priceOverrideService.updatePriceOverride(id, updatePriceOverrideDto);
  }

  @Delete(':id') // DELETE /price-override/:id (Fiziksel silme)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.priceOverrideService.removePriceOverride(id);
  }
}
