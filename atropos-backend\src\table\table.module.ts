// src/table/table.module.ts
import { Module } from '@nestjs/common';
import { TableService } from './table.service';
import { TableController } from './table.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [TableController],
  providers: [TableService],
  exports: [TableService], // Sipariş modülü TableService'e bağımlı olacağı için dışa aktarıyoruz
})
export class TableModule {}
