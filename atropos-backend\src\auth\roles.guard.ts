// src/auth/roles.guard.ts
import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserRole } from '../../generated/prisma'; // UserRole enum'ını import et
import { ROLES_KEY } from './roles.decorator';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<UserRole[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    if (!requiredRoles) {
      return true; // Rol belirtilmemişse herkes erişebilir
    }
    const { user } = context.switchToHttp().getRequest();
    // Kullanıcının rolü, gerek<PERSON> rollerden birini içeriyor mu kontrol et
    return requiredRoles.some((role) => user.role === role);
  }
}
