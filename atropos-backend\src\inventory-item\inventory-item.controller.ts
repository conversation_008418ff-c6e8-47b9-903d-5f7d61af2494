// src/inventory-item/inventory-item.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { InventoryItemService } from './inventory-item.service';
import { CreateInventoryItemDto } from './dto/create-inventory-item.dto';
import { UpdateInventoryItemDto } from './dto/update-inventory-item.dto';

@Controller('inventory-item')
export class InventoryItemController {
  constructor(private readonly inventoryItemService: InventoryItemService) {}

  @Post() // POST /inventory-item
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createInventoryItemDto: CreateInventoryItemDto) {
    return this.inventoryItemService.createInventoryItem(createInventoryItemDto);
  }

  @Get() // GET /inventory-item?productId=...&code=...&supplier=...
  findAll(
    @Query('productId') productId?: string,
    @Query('code') code?: string,
    @Query('supplier') supplier?: string,
  ) {
    return this.inventoryItemService.findAllInventoryItems(productId, code, supplier);
  }

  @Get(':id') // GET /inventory-item/:id
  findOne(@Param('id') id: string) {
    return this.inventoryItemService.findOneInventoryItem(id);
  }

  @Patch(':id') // PATCH /inventory-item/:id
  update(@Param('id') id: string, @Body() updateInventoryItemDto: UpdateInventoryItemDto) {
    return this.inventoryItemService.updateInventoryItem(id, updateInventoryItemDto);
  }

  @Delete(':id') // DELETE /inventory-item/:id (Soft delete)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.inventoryItemService.removeInventoryItem(id);
  }
}
