// src/payment-method/payment-method.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { PaymentMethodService } from './payment-method.service';
import { CreatePaymentMethodDto } from './dto/create-payment-method.dto';
import { UpdatePaymentMethodDto } from './dto/update-payment-method.dto';
import { PaymentMethodType } from '../../generated/prisma';

@Controller('payment-method')
export class PaymentMethodController {
  constructor(private readonly paymentMethodService: PaymentMethodService) {}

  @Post() // POST /payment-method
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createPaymentMethodDto: CreatePaymentMethodDto) {
    return this.paymentMethodService.createPaymentMethod(createPaymentMethodDto);
  }

  @Get() // GET /payment-method?companyId=...
  findAll(@Query('companyId') companyId?: string) {
    return this.paymentMethodService.findAllPaymentMethods(companyId);
  }

  @Get(':id') // GET /payment-method/:id
  findOne(@Param('id') id: string) {
    return this.paymentMethodService.findOnePaymentMethod(id);
  }

  @Patch(':id') // PATCH /payment-method/:id
  update(@Param('id') id: string, @Body() updatePaymentMethodDto: UpdatePaymentMethodDto) {
    return this.paymentMethodService.updatePaymentMethod(id, updatePaymentMethodDto);
  }

  @Delete(':id') // DELETE /payment-method/:id
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.paymentMethodService.removePaymentMethod(id);
  }
}
