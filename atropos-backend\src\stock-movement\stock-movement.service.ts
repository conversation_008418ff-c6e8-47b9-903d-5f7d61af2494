// src/stock-movement/stock-movement.service.ts
import { Injectable, NotFoundException, BadRequestException, ConflictException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateStockMovementDto } from './dto/create-stock-movement.dto';
import { UpdateStockMovementDto } from './dto/update-stock-movement.dto';
import { StockMovementType } from '../../generated/prisma';

@Injectable()
export class StockMovementService {
  constructor(private prisma: PrismaService) {}

  async createStockMovement(data: CreateStockMovementDto) {
    // Branch ve User var mı kontrol et
    const branchExists = await this.prisma.branch.findUnique({ where: { id: data.branchId, deletedAt: null } });
    if (!branchExists) { throw new NotFoundException(`Branch with ID "${data.branchId}" not found.`); }
    const createdByExists = await this.prisma.user.findUnique({ where: { id: data.createdBy, deletedAt: null } });
    if (!createdByExists) { throw new NotFoundException(`User (createdBy) with ID "${data.createdBy}" not found.`); }
    if (data.approvedBy) {
        const approvedByExists = await this.prisma.user.findUnique({ where: { id: data.approvedBy, deletedAt: null } });
        if (!approvedByExists) { throw new NotFoundException(`User (approvedBy) with ID "${data.approvedBy}" not found.`); }
    }
    
    // productId veya inventoryItemId'den sadece birinin dolu olması kuralı
    if ((data.productId && data.inventoryItemId) || (!data.productId && !data.inventoryItemId)) {
      throw new BadRequestException('Either productId or inventoryItemId must be provided, but not both.');
    }

    let targetInventoryItem: any; // InventoryItem veya Product'ı temsil edecek
    let stockUpdateTarget: 'product' | 'inventoryItem'; // Stok güncellenecek model

    if (data.productId) {
      const product = await this.prisma.product.findUnique({ where: { id: data.productId, deletedAt: null } });
      if (!product) { throw new NotFoundException(`Product with ID "${data.productId}" not found.`); }
      // Eğer Product'ın kendi InventoryItem'ı varsa onu kullan
      targetInventoryItem = await this.prisma.inventoryItem.findFirst({ where: { productId: data.productId, deletedAt: null } });
      if (!targetInventoryItem) { // Eğer ürünün bir inventoryItem'ı yoksa, ürünün kendisini hedef al
        targetInventoryItem = product;
        stockUpdateTarget = 'product';
      } else {
        stockUpdateTarget = 'inventoryItem';
      }
    } else { // inventoryItemId doluysa
      const inventoryItem = await this.prisma.inventoryItem.findUnique({ where: { id: data.inventoryItemId, deletedAt: null } });
      if (!inventoryItem) { throw new NotFoundException(`Inventory item with ID "${data.inventoryItemId}" not found.`); }
      targetInventoryItem = inventoryItem;
      stockUpdateTarget = 'inventoryItem';
    }

    // Birim uyumluluğu kontrolü (ProductUnit)
    if (targetInventoryItem.unit !== data.unit) {
      throw new BadRequestException(`Unit mismatch: Target item unit is ${targetInventoryItem.unit}, but movement unit is ${data.unit}.`);
    }

    // Miktar ayarlaması: SALE, REFUND (çıkış), WASTE, DAMAGE, THEFT, TRANSFER_OUT, CONSUMPTION, SAMPLE, GIFT için negatif miktar
    let adjustedQuantity = parseFloat(data.quantity.toFixed(3));
    const negativeTypes = [
      StockMovementType.SALE, StockMovementType.RETURN_OUT, StockMovementType.WASTE,
      StockMovementType.DAMAGE, StockMovementType.THEFT, StockMovementType.TRANSFER_OUT,
      StockMovementType.CONSUMPTION, StockMovementType.SAMPLE, StockMovementType.GIFT,
      StockMovementType.MODIFIER_CONSUMPTION
    ];
    if (negativeTypes.includes(data.type as any)) {
      adjustedQuantity = -Math.abs(adjustedQuantity); // Miktarı negatif yap
    }

    const previousStock = stockUpdateTarget === 'product' ? targetInventoryItem.currentStock.toNumber() : targetInventoryItem.currentStock.toNumber();
    const newCurrentStock = previousStock + adjustedQuantity;

    if (newCurrentStock < 0 && !['ADJUSTMENT'].includes(data.type)) { // Negatif stoka düşmemesi için (Adjustments hariç)
        throw new BadRequestException(`Insufficient stock for this movement. Current stock: ${previousStock.toFixed(3)} ${data.unit}. Attempted movement: ${adjustedQuantity.toFixed(3)} ${data.unit}.`);
    }

    // Maliyet hesaplamaları (Average Cost Method - Hareketli Ağırlıklı Ortalama)
    let previousCost = targetInventoryItem.averageCost?.toNumber() || 0;
    let newAverageCost = previousCost;
    let totalCost = 0; // Bu hareketin maliyeti

    if (data.type === StockMovementType.PURCHASE) {
      if (data.unitCost === undefined || data.unitCost < 0) {
        throw new BadRequestException('Unit cost must be provided and positive for PURCHASE movements.');
      }
      const incomingCost = data.unitCost * data.quantity;
      totalCost = parseFloat(incomingCost.toFixed(2));

      if (previousStock + data.quantity > 0) { // Bölme sıfır olmasın
         newAverageCost = ((previousStock * previousCost) + incomingCost) / (previousStock + data.quantity);
      } else {
         newAverageCost = data.unitCost; // Eğer stok sıfırsa ve yeni alım yapılıyorsa
      }
    } else { // Çıkış veya diğer hareketler
      totalCost = Math.abs(adjustedQuantity) * previousCost; // Çıkışın maliyeti ortalama maliyetle hesaplanır
      // Stok azalırken ortalama maliyet değişmez
      newAverageCost = previousCost;
    }

    // Veritabanı işlemi
    const createdMovement = await this.prisma.stockMovement.create({
      data: {
        ...data,
        quantity: adjustedQuantity, // Adjusted quantity'yi kaydet
        unitCost: data.unitCost !== undefined ? parseFloat(data.unitCost.toFixed(2)) : undefined,
        totalCost: parseFloat(totalCost.toFixed(2)),
        previousCost: parseFloat(previousCost.toFixed(2)),
        newAverageCost: parseFloat(newAverageCost.toFixed(2)),
        previousStock: parseFloat(previousStock.toFixed(3)),
        currentStock: parseFloat(newCurrentStock.toFixed(3)),
        productId: data.productId, // Nullable olabilir
        inventoryItemId: data.inventoryItemId, // Nullable olabilir
      },
    });

    // InventoryItem veya Product'taki stok ve maliyet bilgilerini güncelle
    const updateData: any = {
      currentStock: parseFloat(newCurrentStock.toFixed(3)),
      availableStock: parseFloat((newCurrentStock - (targetInventoryItem.reservedStock?.toNumber() || 0)).toFixed(3)),
      averageCost: parseFloat(newAverageCost.toFixed(2)),
    };
    
    if (stockUpdateTarget === 'product') { // Product modelinde stock alanı yok
      // Product'taki trackStock: true olanlar için
      // Şemada Product'ta currentStock veya averageCost yok, InventoryItem'da var.
      // Bu durumda Product doğrudan güncellenmez.
      // Ya Product için de stok alanları eklenmeli ya da sadece InventoryItem hedef alınmalı.
      // Mevcut şemaya göre Product'ın stok alanı yok. Bu durumda productId verilse bile
      // ilgili InventoryItem'ı bulup onu güncellemeliyiz.
      // createStockMovement'ın başındaki targetInventoryItem bulma mantığını bu duruma göre düzeltelim.
      // (Yukarıdaki kodda bu düzeltme yapıldı: her durumda InventoryItem'ı hedefliyoruz, Product.trackStock: true ise InventoryItem.productId'si dolu olur)
    } else { // inventoryItem hedefliyorsak
      await this.prisma.inventoryItem.update({
        where: { id: targetInventoryItem.id },
        data: updateData,
      });
    }

    return createdMovement;
  }

  async findAllStockMovements(
    branchId?: string,
    productId?: string,
    inventoryItemId?: string,
    type?: StockMovementType,
    startDate?: Date,
    endDate?: Date,
  ) {
    return this.prisma.stockMovement.findMany({
      where: {
        branchId: branchId || undefined,
        productId: productId || undefined,
        inventoryItemId: inventoryItemId || undefined,
        type: type || undefined,
        createdAt: {
          gte: startDate || undefined,
          lte: endDate ? new Date(endDate.getTime() + (24 * 60 * 60 * 1000) - 1) : undefined, // Gün sonunu dahil et
        },
      },
      include: {
        branch: { select: { id: true, name: true } },
        product: { select: { id: true, name: true, code: true } },
        inventoryItem: { select: { id: true, name: true, code: true } },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOneStockMovement(id: string) {
    const movement = await this.prisma.stockMovement.findUnique({
      where: { id },
      include: {
        branch: { select: { id: true, name: true } },
        product: { select: { id: true, name: true, code: true } },
        inventoryItem: { select: { id: true, name: true, code: true } },
      },
    });
    if (!movement) {
      throw new NotFoundException(`Stock movement with ID "${id}" not found.`);
    }
    return movement;
  }

  async updateStockMovement(id: string, data: UpdateStockMovementDto) {
    // Stok hareketlerinin güncellenmesi, sonraki hareketlerin bakiyelerini etkileyeceği için çok karmaşıktır.
    // Genellikle finansal hareketler gibi, stok hareketleri de güncellenmez,
    // bunun yerine "iptal" edilip yeni bir düzeltme hareketi oluşturulur.
    // Şimdilik sadece "approvedBy" ve "approvedAt" gibi denetim alanlarının güncellenmesine izin veriyoruz.
    const existingMovement = await this.findOneStockMovement(id);

    if ((data as any).quantity !== undefined || (data as any).type !== undefined || (data as any).unit !== undefined || (data as any).unitCost !== undefined ||
        (data as any).productId !== undefined || (data as any).inventoryItemId !== undefined || (data as any).branchId !== undefined || (data as any).createdBy !== undefined) {
        throw new ForbiddenException('Only "approvedBy", "approvedAt", "reason", "note", "referenceNo", "attachments" can be updated for stock movements. For other changes, consider voiding and re-creating the movement.');
    }

    try {
      return await this.prisma.stockMovement.update({
        where: { id },
        data,
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Stock movement with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removeStockMovement(id: string) {
    // Stok hareketleri genellikle fiziksel olarak silinmez, bunun yerine durumları güncellenir
    // veya telafi edici yeni bir hareket oluşturulur.
    // Şemada deletedAt alanı olmadığı için fiziksel silme yapıyoruz.
    // Bu silme işlemi, ilgili inventoryItem'ın stok ve maliyetini tekrar etkileyecektir,
    // ancak bu karmaşık geri alma mantığı şu anki kapsamın dışındadır.
    try {
      const movement = await this.prisma.stockMovement.delete({
        where: { id },
      });

      // Not: Bu silme sonrası, etkilenen inventoryItem'ın stok ve maliyetinin geri alınması gerekir.
      // Bu, çok karmaşık bir senaryodur ve bu modülün ilk versiyonunda ele alınmayacaktır.

      return movement;
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Stock movement with ID "${id}" not found.`);
      }
      throw error;
    }
  }
}
