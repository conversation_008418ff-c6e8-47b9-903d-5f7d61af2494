// src/table/table.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { TableService } from './table.service';
import { CreateTableDto } from './dto/create-table.dto';
import { UpdateTableDto } from './dto/update-table.dto';

@Controller('table')
export class TableController {
  constructor(private readonly tableService: TableService) {}

  @Post() // POST /table
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createTableDto: CreateTableDto) {
    return this.tableService.createTable(createTableDto);
  }

  @Get() // GET /table?branchId=...&areaId=...&status=...
  findAll(
    @Query('branchId') branchId?: string,
    @Query('areaId') areaId?: string,
    @Query('status') status?: string, // TableStatus enum değerlerinden biri
  ) {
    return this.tableService.findAllTables(branchId, areaId, status);
  }

  @Get(':id') // GET /table/:id
  findOne(@Param('id') id: string) {
    return this.tableService.findOneTable(id);
  }

  @Patch(':id') // PATCH /table/:id
  update(@Param('id') id: string, @Body() updateTableDto: UpdateTableDto) {
    return this.tableService.updateTable(id, updateTableDto);
  }

  @Delete(':id') // DELETE /table/:id
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.tableService.removeTable(id);
  }
}
