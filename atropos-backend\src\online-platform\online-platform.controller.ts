// src/online-platform/online-platform.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { OnlinePlatformService } from './online-platform.service';
import { CreateOnlinePlatformDto } from './dto/create-online-platform.dto';
import { UpdateOnlinePlatformDto } from './dto/update-online-platform.dto';

@Controller('online-platform')
export class OnlinePlatformController {
  constructor(private readonly onlinePlatformService: OnlinePlatformService) {}

  @Post() // POST /online-platform
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createOnlinePlatformDto: CreateOnlinePlatformDto) {
    return this.onlinePlatformService.createOnlinePlatform(createOnlinePlatformDto);
  }

  @Get() // GET /online-platform?companyId=...
  findAll(@Query('companyId') companyId?: string) {
    return this.onlinePlatformService.findAllOnlinePlatforms(companyId);
  }

  @Get(':id') // GET /online-platform/:id
  findOne(@Param('id') id: string) {
    return this.onlinePlatformService.findOneOnlinePlatform(id);
  }

  @Patch(':id') // PATCH /online-platform/:id
  update(@Param('id') id: string, @Body() updateOnlinePlatformDto: UpdateOnlinePlatformDto) {
    return this.onlinePlatformService.updateOnlinePlatform(id, updateOnlinePlatformDto);
  }

  @Delete(':id') // DELETE /online-platform/:id (Fiziksel silme)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.onlinePlatformService.removeOnlinePlatform(id);
  }
}
