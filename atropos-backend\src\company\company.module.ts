// src/company/company.module.ts
import { Module } from '@nestjs/common';
import { CompanyController } from './company.controller';
import { CompanyService } from './company.service';
import { PrismaModule } from '../prisma/prisma.module'; // PrismaModule'ü içe aktar

@Module({
  imports: [PrismaModule], // CompanyService Prisma'ya bağımlı olduğu için buraya ekliyoruz
  controllers: [CompanyController],
  providers: [CompanyService],
  exports: [CompanyService] // Eğer CompanyService'i başka modüllerde kullanacaksan dışa aktar
})
export class CompanyModule {}