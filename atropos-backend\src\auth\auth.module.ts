// src/auth/auth.module.ts
import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { UserModule } from '../user/user.module'; // UserModule'ü import et
import { PassportModule } from '@nestjs/passport'; // PassportModule'ü import et
import { JwtModule } from '@nestjs/jwt'; // JwtModule'ü import et
import { ConfigModule, ConfigService } from '@nestjs/config'; // ConfigModule ve ConfigService'i import et
import { LocalStrategy } from './local.strategy'; // LocalStrategy'i import et
import { JwtStrategy } from './jwt.strategy'; // JwtStrategy'i import et
import { LocalAuthGuard } from './local-auth.guard'; // Guard'ları import et
import { JwtAuthGuard } from './jwt-auth.guard'; // Guard'ları import et
import { RolesGuard } from './roles.guard'; // Guard'ları import et
import { PrismaModule } from '../prisma/prisma.module'; // PrismaModule'ü import et

@Module({
  imports: [
    PrismaModule, // PrismaService'i kullanabilmek için
    UserModule, // AuthService, UserService'e bağımlı
    PassportModule, // Kimlik doğrulama stratejileri için
    JwtModule.registerAsync({ // JwtModule'ü asenkron olarak kaydet (ConfigService kullanabilmek için)
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: { expiresIn: configService.get<string>('JWT_EXPIRES_IN') },
      }),
      inject: [ConfigService],
    }),
    ConfigModule, // Ortam değişkenlerini kullanmak için
  ],
  controllers: [AuthController],
  providers: [AuthService, LocalStrategy, JwtStrategy, LocalAuthGuard, JwtAuthGuard, RolesGuard], // Tüm servis, strateji ve Guard'ları ekle
  exports: [AuthService, JwtModule, LocalAuthGuard, JwtAuthGuard, RolesGuard], // Diğer modüllerin kullanması için export et
})
export class AuthModule {}
