// src/printer-group/dto/create-printer-group.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsArray,
} from 'class-validator';

export class CreatePrinterGroupDto {
  @IsString()
  @IsNotEmpty()
  name: string; // "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>" (ben<PERSON><PERSON> olmalı)

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  categoryIds?: string[]; // Bu gruba atanacak kategori ID'leri
}
