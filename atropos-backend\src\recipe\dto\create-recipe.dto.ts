// src/recipe/dto/create-recipe.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsNumber,
  Min,
  IsInt,
  IsBoolean,
  IsArray,
  ValidateNested,
  ArrayMinSize,
  IsEnum,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';

// ProductUnit enum'unu manuel olarak tanımlayalım
enum ProductUnit {
  PIECE = 'PIECE',
  KG = 'KG',
  GRAM = 'GRAM',
  LITER = 'LITER',
  ML = 'ML',
}

// Reçete Kalemi için DTO
export class CreateRecipeItemDto {
  @IsString()
  @IsNotEmpty()
  inventoryItemId: string; // Hammadde ID'si

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 3 })
  @Min(0.001)
  @IsNotEmpty()
  quantity: number;

  @IsEnum(ProductUnit)
  @IsNotEmpty()
  unit: ProductUnit; // Hammadde birimi

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  @IsOptional()
  wastagePercent?: number; // Fire oranı (yüzde)
}

export class CreateRecipeDto {
  @IsString()
  @IsNotEmpty()
  productId: string; // Hangi nihai ürüne ait olduğu (benzersiz olmalı)

  @IsString()
  @IsNotEmpty()
  name: string; // Reçete adı (örn: "Tavuk Burger Reçetesi")

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 3 })
  @Min(0.001)
  @IsNotEmpty()
  yield: number; // Kaç porsiyon/birim ürün ürettiği

  @IsString()
  @IsOptional()
  preparationSteps?: string; // JSON array of steps (metin olarak saklanabilir)

  @IsInt()
  @Min(0)
  @IsOptional()
  preparationTime?: number; // Dakika olarak hazırlık süresi

  @IsBoolean()
  @IsOptional()
  active?: boolean;

  // Reçete kalemleri
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => CreateRecipeItemDto)
  items: CreateRecipeItemDto[];
}
