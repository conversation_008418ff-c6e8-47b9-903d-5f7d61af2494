// src/table-merge/table-merge.module.ts
import { Module } from '@nestjs/common';
import { TableMergeService } from './table-merge.service';
import { TableMergeController } from './table-merge.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [TableMergeController],
  providers: [TableMergeService],
  exports: [TableMergeService],
})
export class TableMergeModule {}
