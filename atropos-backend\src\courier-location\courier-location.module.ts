// src/courier-location/courier-location.module.ts
import { Module } from '@nestjs/common';
import { CourierLocationService } from './courier-location.service';
import { CourierLocationController } from './courier-location.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [CourierLocationController],
  providers: [CourierLocationService],
  exports: [CourierLocationService],
})
export class CourierLocationModule {}
