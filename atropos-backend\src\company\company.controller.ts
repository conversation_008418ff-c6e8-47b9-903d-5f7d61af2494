// src/company/company.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
  UseGuards, // UseGuards'ı import et
  Request, // Request'i import et (kullanıcıya erişmek için)
} from '@nestjs/common';
import { CompanyService } from './company.service';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard'; // JwtAuthGuard'ı import et
import { RolesGuard } from '../auth/roles.guard'; // RolesGuard'ı import et
import { Roles } from '../auth/roles.decorator'; // Roles decorator'ı import et
import { UserRole } from '../../generated/prisma'; // UserRole enum'ını import et

@Controller('company')
export class CompanyController {
  constructor(private readonly companyService: CompanyService) {}

  @UseGuards(JwtAuthGuard, RolesGuard) // Hem kimlik doğrulama hem yetkilendirme
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN) // Sadece ADMIN ve SUPER_ADMIN rolündekiler oluşturabilir
  @Post() // POST /company
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createCompanyDto: CreateCompanyDto, @Request() req) {
    console.log(`User ${req.user.username} (Role: ${req.user.role}) is creating a company.`); // Loglama
    return this.companyService.createCompany(createCompanyDto);
  }

  @Get() // GET /company (Zaten vardı, üzerine yazdık)
  findAll() {
    return this.companyService.findAllCompanies();
  }

  @Get(':id') // GET /company/:id
  findOne(@Param('id') id: string) {
    return this.companyService.findOneCompany(id);
  }

  // Güncelleme ve silme işlemleri genellikle Admin yetkisi gerektirir
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @Patch(':id')
  update(@Param('id') id: string, @Body() updateCompanyDto: UpdateCompanyDto) {
    return this.companyService.updateCompany(id, updateCompanyDto);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.companyService.removeCompany(id);
  }
}