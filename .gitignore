# Atropos POS System - Root .gitignore

# Dependencies
node_modules/
*/node_modules/

# Build outputs
dist/
*/dist/
build/
*/build/
out/
*/out/
dist-electron/
*/dist-electron/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.env
*/.env
*/.env.local
*/.env.development.local
*/.env.test.local
*/.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*/coverage/
.nyc_output/
*/.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Lock files (keep package-lock.json but ignore others)
yarn.lock
*/yarn.lock
pnpm-lock.yaml
*/pnpm-lock.yaml

# IDEs and editors
.idea/
.vscode/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Database files
*.db
*.sqlite
*.sqlite3

# Prisma
generated/
*/generated/
prisma/migrations/*
*/prisma/migrations/*
!prisma/migrations/.gitkeep
!*/prisma/migrations/.gitkeep

# Uploads and media
uploads/
*/uploads/
public/uploads/
*/public/uploads/

# Cache directories
.cache/
*/.cache/
.parcel-cache/
*/.parcel-cache/
.vite/
*/.vite/

# Temporary files
*.tmp
*.temp
.temp/
*/.temp/
.tmp/
*/.tmp/

# Backup files
*.backup
*.bak

# Test files
test-data.json
*/test-data.json
invalid-test-data.json
*/invalid-test-data.json

# Documentation build
docs/build/
*/docs/build/

# Electron specific
release/
*/release/
app/dist/
*/app/dist/
app/node_modules/
*/app/node_modules/

# Local development
.local/
*/.local/

# Package manager files
.pnp
.pnp.js

# Diagnostic reports
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor backup files
*~
*.swp
*.swo

# JetBrains IDEs
.idea/

# Visual Studio Code
.vscode/
!.vscode/extensions.json

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*
