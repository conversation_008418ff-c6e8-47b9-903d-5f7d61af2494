// src/audit-log/audit-log.module.ts
import { Modu<PERSON> } from '@nestjs/common';
import { AuditLogService } from './audit-log.service';
import { AuditLogController } from './audit-log.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [AuditLogController],
  providers: [AuditLogService],
  exports: [AuditLogService], // Diğer modüller log kaydı oluşturmak için kullanabilir
})
export class AuditLogModule {}
