// src/user/dto/create-user.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEmail,
  IsEnum,
  IsDateString,
  MinLength,
  IsBoolean,
} from 'class-validator';
import { UserRole } from '../../../generated/prisma'; // Prisma'dan UserRole enum'ını import et

export class CreateUserDto {
  @IsString()
  @IsNotEmpty()
  companyId: string;

  @IsString()
  @IsOptional()
  branchId?: string; // Nullable = tüm şubelere erişim

  @IsString()
  @IsNotEmpty()
  username: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(8) // Şifre için minimum uzunluk
  password: string;

  @IsString()
  @IsOptional()
  pin?: string; // Hızlı giriş PIN'i (hashlenmiş olarak saklanacak)

  @IsString()
  @IsNotEmpty()
  firstName: string;

  @IsString()
  @IsNotEmpty()
  lastName: string;

  @IsString()
  @IsOptional()
  @IsEmail()
  email?: string;

  @IsString()
  @IsOptional()
  phone?: string;

  @IsString()
  @IsOptional()
  avatar?: string;

  @IsEnum(UserRole)
  @IsNotEmpty()
  role: UserRole;

  // permissions Json? (DTO'da JSON type için özel bir validatör yoktur,
  // genellikle uygulama mantığında veya bir pipe ile doğrulanır)

  @IsString()
  @IsOptional()
  employeeCode?: string;

  @IsOptional()
  @IsDateString()
  hireDate?: Date;

  @IsOptional()
  @IsDateString()
  birthDate?: Date;

  @IsString()
  @IsOptional()
  nationalId?: string;

  @IsString()
  @IsOptional()
  vehicleType?: string; // "motorcycle", "bicycle", "car"

  @IsString()
  @IsOptional()
  vehiclePlate?: string;

  @IsOptional()
  @IsBoolean()
  active?: boolean;
}
