// src/courier-location/dto/update-courier-location.dto.ts
import { PartialType } from '@nestjs/mapped-types';
import { CreateCourierLocationDto } from './create-courier-location.dto';
import { IsString, IsOptional, IsDateString } from 'class-validator';

export class UpdateCourierLocationDto extends PartialType(CreateCourierLocationDto) {
  // Konum kayıtları genellikle değiştirilmez, sadece yeni kayıt eklenir.
  // Ancak "yanlış girilmiş konum" gibi nadir durumlar için opsiyonel alanlar bırakılabilir.
  // `timestamp` gibi zaman damgaları veya `expiresAt` gibi alanlar güncellenebilir.
  @IsOptional()
  @IsDateString()
  timestamp?: Date;
}
