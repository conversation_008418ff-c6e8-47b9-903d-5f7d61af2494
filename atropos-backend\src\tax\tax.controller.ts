// src/tax/tax.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { TaxService } from './tax.service';
import { CreateTaxDto } from './dto/create-tax.dto';
import { UpdateTaxDto } from './dto/update-tax.dto';

@Controller('tax')
export class TaxController {
  constructor(private readonly taxService: TaxService) {}

  @Post() // POST /tax
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createTaxDto: CreateTaxDto) {
    return this.taxService.createTax(createTaxDto);
  }

  @Get() // GET /tax?companyId=...
  findAll(@Query('companyId') companyId?: string) {
    return this.taxService.findAllTaxes(companyId);
  }

  @Get(':id') // GET /tax/:id
  findOne(@Param('id') id: string) {
    return this.taxService.findOneTax(id);
  }

  @Patch(':id') // PATCH /tax/:id
  update(@Param('id') id: string, @Body() updateTaxDto: UpdateTaxDto) {
    return this.taxService.updateTax(id, updateTaxDto);
  }

  @Delete(':id') // DELETE /tax/:id
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.taxService.removeTax(id);
  }
}
