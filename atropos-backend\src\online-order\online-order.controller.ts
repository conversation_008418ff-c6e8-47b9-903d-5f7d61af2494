// src/online-order/online-order.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,

} from '@nestjs/common';
import { OnlineOrderService } from './online-order.service';
import { CreateOnlineOrderDto } from './dto/create-online-order.dto';
import { UpdateOnlineOrderDto } from './dto/update-online-order.dto';
// OnlineOrderStatus enum'unu manuel olarak tanımlayalım
enum OnlineOrderStatus {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
  PREPARING = 'PREPARING',
  READY = 'READY',
  DELIVERING = 'DELIVERING',
  DELIVERED = 'DELIVERED',
  CANCELLED = 'CANCELLED',
  RETURNED = 'RETURNED',
}
import { ParseOptionalDatePipe } from '../common/pipes/parse-optional-date.pipe'; // Tarih <PERSON>'ı
import { ParseOptionalEnumPipe } from '../common/pipes/parse-optional-enum.pipe'; // Enum Pipe'ı

@Controller('online-order')
export class OnlineOrderController {
  constructor(private readonly onlineOrderService: OnlineOrderService) {}

  @Post() // POST /online-order
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createOnlineOrderDto: CreateOnlineOrderDto) {
    return this.onlineOrderService.createOnlineOrder(createOnlineOrderDto);
  }

  @Get() // GET /online-order?platformId=...&orderId=...&status=...&platformOrderNo=...&startDate=...&endDate=...
  findAll(
    @Query('platformId') platformId?: string,
    @Query('orderId') orderId?: string,
    @Query('status', new ParseOptionalEnumPipe(OnlineOrderStatus)) status?: OnlineOrderStatus,
    @Query('platformOrderNo') platformOrderNo?: string,
    @Query('startDate', ParseOptionalDatePipe) startDate?: Date,
    @Query('endDate', ParseOptionalDatePipe) endDate?: Date,
  ) {
    return this.onlineOrderService.findAllOnlineOrders(
      platformId,
      orderId,
      status,
      platformOrderNo,
      startDate,
      endDate,
    );
  }

  @Get(':id') // GET /online-order/:id
  findOne(@Param('id') id: string) {
    return this.onlineOrderService.findOneOnlineOrder(id);
  }

  @Patch(':id') // PATCH /online-order/:id
  update(@Param('id') id: string, @Body() updateOnlineOrderDto: UpdateOnlineOrderDto) {
    return this.onlineOrderService.updateOnlineOrder(id, updateOnlineOrderDto);
  }

  @Delete(':id') // DELETE /online-order/:id (Fiziksel silme)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.onlineOrderService.removeOnlineOrder(id);
  }
}
