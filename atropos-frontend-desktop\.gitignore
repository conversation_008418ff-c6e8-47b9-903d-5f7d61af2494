# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules

# Build outputs
dist
dist-ssr
dist-electron
build
out

# Environment files
*.local
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
Thumbs.db
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Electron
/release
/app/dist
/app/node_modules

# Cache
.cache
.parcel-cache
.vite

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Lock files (keep package-lock.json but ignore others)
yarn.lock
pnpm-lock.yaml

# Temporary files
*.tmp
*.temp
