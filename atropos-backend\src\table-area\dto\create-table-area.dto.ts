// src/table-area/dto/create-table-area.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsInt,
  Min,
  IsBoolean,
} from 'class-validator';

export class CreateTableAreaDto {
  @IsString()
  @IsNotEmpty()
  branchId: string; // Hangi şubeye bağlı olduğu

  @IsString()
  @IsNotEmpty()
  name: string; // "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>ç Mekan", "Teras"

  @IsString()
  @IsOptional()
  description?: string;

  @IsInt()
  @IsOptional()
  @Min(0)
  displayOrder?: number;

  @IsBoolean()
  @IsOptional()
  active?: boolean;

  @IsBoolean()
  @IsOptional()
  smokingAllowed?: boolean;
}
