// src/common/filters/prisma-exception.filter.ts
import {
  Catch,
  ArgumentsHost,
  ExceptionFilter,
  HttpStatus,
} from '@nestjs/common';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library'; // PrismaClientKnownRequestError doğru import yolu
import { Response } from 'express';

@Catch(PrismaClientKnownRequestError)
export class PrismaExceptionFilter implements ExceptionFilter {
  catch(exception: PrismaClientKnownRequestError, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'An unexpected error occurred.';
    let error = 'Internal Server Error';

    switch (exception.code) {
      case 'P2000': // The value provided for the column is too long
        status = HttpStatus.BAD_REQUEST;
        message = `The value for column '${(exception.meta as any)?.target || 'unknown'}' is too long.`;
        error = 'Bad Request';
        break;
      case 'P2002': // Unique constraint violation
        status = HttpStatus.CONFLICT;
        message = `Duplicate entry for unique field: ${(exception.meta as any)?.target}.`;
        error = 'Conflict';
        break;
      case 'P2025': // Record to update/delete/find was not found
        status = HttpStatus.NOT_FOUND;
        message = `Record not found. ${(exception.meta as any)?.cause || ''}`;
        error = 'Not Found';
        break;
      // Diğer Prisma hata kodlarını buraya ekleyebilirsin
      // Örneğin: P2003 (Foreign key constraint violation)
      case 'P2003': // Foreign key constraint violation
        status = HttpStatus.BAD_REQUEST;
        message = `Foreign key constraint failed on the field: ${(exception.meta as any)?.field_name || 'unknown'}. This record is referenced by another entity.`;
        error = 'Bad Request';
        break;
      default:
        // Beklenmedik diğer Prisma hataları
        console.error('Unhandled Prisma error:', exception);
        message = 'An unexpected database error occurred.';
        break;
    }

    response.status(status).json({
      statusCode: status,
      message: message,
      error: error,
      timestamp: new Date().toISOString(),
      path: request.url,
    });
  }
}
