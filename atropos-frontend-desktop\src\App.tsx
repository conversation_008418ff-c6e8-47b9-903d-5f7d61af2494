// src/App.tsx
import React, { useState, useEffect } from 'react';
import './App.css';
import LoginScreen from './auth/LoginScreen'; // LoginScreen'i import et
import MainDashboard from './dashboard/MainDashboard'; // MainDashboard'u import et

function App() {
  const [token, setToken] = useState<string | null>(null); // JWT token'ı state'i

  // Uygulama yüklendiğinde token'ı localStorage'dan kontrol et
  useEffect(() => {
    const storedToken = localStorage.getItem('atropos_pos_token');
    if (storedToken) {
      setToken(storedToken);
    }
  }, []);

  const handleLoginSuccess = (newToken: string) => {
    setToken(newToken);
    localStorage.setItem('atropos_pos_token', newToken); // Token'ı localStorage'da sakla
    // Başarılı giriş sonrası kullanıcıyı Dashboard'a yönlendir
    // (Şu an için direkt state değiş<PERSON><PERSON> ile render edilecek)
  };

  const handleLogout = () => {
    setToken(null);
    localStorage.removeItem('atropos_pos_token'); // Token'ı sil
    // Kullanıcıyı Login ekranına yönlendir
  };

  // Token varsa Dashboard'ı, yoksa LoginScreen'i render et
  return (
    <div className="App-container"> {/* App.css'de bu stilin tanımlı olduğundan emin ol */}
      {token ? (
        <MainDashboard onLogout={handleLogout} />
      ) : (
        <LoginScreen onLoginSuccess={handleLoginSuccess} />
      )}
    </div>
  );
}

export default App;
