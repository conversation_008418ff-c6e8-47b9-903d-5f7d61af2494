// src/online-product-mapping/dto/create-online-product-mapping.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  IsNumber,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateOnlineProductMappingDto {
  @IsString()
  @IsNotEmpty()
  platformId: string; // Hangi online platforma ait olduğu

  @IsString()
  @IsNotEmpty()
  productId: string; // <PERSON><PERSON> sistemimizdeki ürün ID'si

  @IsString()
  @IsNotEmpty()
  platformProductId: string; // Online platformdaki ürün ID'si

  @IsString()
  @IsOptional()
  platformBarcode?: string; // Online platformdaki ürün barkodu

  @IsBoolean()
  @IsOptional()
  isActive?: boolean; // Aktif eşleşme mi?

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  priceOverride?: number; // Platforma özel fiyat
}
