// src/courier-location/courier-location.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { CourierLocationService } from './courier-location.service';
import { CreateCourierLocationDto } from './dto/create-courier-location.dto';
import { UpdateCourierLocationDto } from './dto/update-courier-location.dto';
import { ParseOptionalDatePipe } from '../common/pipes/parse-optional-date.pipe'; // Tarih <PERSON>'ı

@Controller('courier-location')
export class CourierLocationController {
  constructor(private readonly courierLocationService: CourierLocationService) {}

  @Post() // POST /courier-location
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createCourierLocationDto: CreateCourierLocationDto) {
    return this.courierLocationService.createCourierLocation(createCourierLocationDto);
  }

  @Get() // GET /courier-location?courierId=...&branchId=...&startDate=...&endDate=...
  findAll(
    @Query('courierId') courierId?: string,
    @Query('branchId') branchId?: string,
    @Query('startDate', ParseOptionalDatePipe) startDate?: Date,
    @Query('endDate', ParseOptionalDatePipe) endDate?: Date,
  ) {
    return this.courierLocationService.findAllCourierLocations(
      courierId,
      branchId,
      startDate,
      endDate,
    );
  }

  @Get(':id') // GET /courier-location/:id
  findOne(@Param('id') id: string) {
    return this.courierLocationService.findOneCourierLocation(id);
  }

  @Patch(':id') // PATCH /courier-location/:id
  update(@Param('id') id: string, @Body() updateCourierLocationDto: UpdateCourierLocationDto) {
    return this.courierLocationService.updateCourierLocation(id, updateCourierLocationDto);
  }

  @Delete(':id') // DELETE /courier-location/:id (Fiziksel silme)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.courierLocationService.removeCourierLocation(id);
  }
}
