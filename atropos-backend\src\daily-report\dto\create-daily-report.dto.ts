// src/daily-report/dto/create-daily-report.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsDateString,
  IsNumber,
  Min,
  IsInt,
  IsJSON,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateDailyReportDto {
  @IsString()
  @IsNotEmpty()
  branchId: string;

  @IsDateString()
  @IsNotEmpty()
  reportDate: Date; // Yalnızca tarih kısmı kullanılacak

  @IsString()
  @IsNotEmpty()
  createdBy: string; // Raporu oluşturan kullanıcı ID'si

  // Raporlama alanları servis tarafından hesaplanacak, DTO'da beklenmiyor
  // ancak manuel giriş veya overriding için opsiyonel bırakılabilir
  @IsInt()
  @Min(0)
  @IsOptional()
  totalOrders?: number;

  @IsInt()
  @Min(0)
  @IsOptional()
  totalItems?: number;

  @IsInt()
  @Min(0)
  @IsOptional()
  totalCustomers?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  averageTicket?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  grossSales?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  totalDiscount?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  totalServiceCharge?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  netSales?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  totalTax?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  totalSales?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  cashSales?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  creditCardSales?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  debitCardSales?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  mealCardSales?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  otherSales?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  totalReturns?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  totalCancellations?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  openingBalance?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  totalCashIn?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  totalCashOut?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  expectedBalance?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  actualBalance?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  difference?: number;

  @IsOptional()
  taxBreakdown?: any; // JSON
  @IsOptional()
  categoryBreakdown?: any; // JSON
  @IsOptional()
  hourlyBreakdown?: any; // JSON

  @IsString()
  @IsOptional()
  zReportNo?: string;

  @IsString()
  @IsOptional()
  fiscalId?: string;

  @IsString()
  @IsOptional()
  approvedBy?: string;
}
