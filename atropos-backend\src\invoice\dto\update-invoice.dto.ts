// src/invoice/dto/update-invoice.dto.ts
import { PartialType } from '@nestjs/mapped-types';
import { CreateInvoiceDto } from './create-invoice.dto';
import { IsString, IsOptional, IsBoolean, IsEnum, IsDateString, IsNumber, Min } from 'class-validator';
import { Type } from 'class-transformer';
// Enum'ı object olarak tanımlayalım
const EArchiveStatus = {
  PENDING: 'PENDING',
  SENT: 'SENT',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  CANCELLED: 'CANCELLED'
} as const;

type EArchiveStatus = typeof EArchiveStatus[keyof typeof EArchiveStatus];

export class UpdateInvoiceDto extends PartialType(CreateInvoiceDto) {
  @IsString()
  @IsOptional()
  serialNo?: string;

  @IsString()
  @IsOptional()
  sequenceNo?: string;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  subtotal?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  discountAmount?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  taxAmount?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  totalAmount?: number;
  @IsOptional()
  @IsDateString()
  printedAt?: Date;

  @IsOptional()
  @IsDateString()
  sentAt?: Date;

  @IsOptional()
  @IsDateString()
  viewedAt?: Date;

  @IsOptional()
  @IsBoolean()
  isCancelled?: boolean; // Faturayı iptal etmek için

  @IsString()
  @IsOptional()
  cancelReason?: string;

  @IsString()
  @IsOptional()
  cancelledInvoiceId?: string;

  @IsEnum(EArchiveStatus)
  @IsOptional()
  eArchiveStatus?: EArchiveStatus; // E-arşiv durumunu manuel güncelleme
}
