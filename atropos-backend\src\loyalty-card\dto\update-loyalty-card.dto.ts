// src/loyalty-card/dto/update-loyalty-card.dto.ts
import { PartialType } from '@nestjs/mapped-types';
import { CreateLoyaltyCardDto } from './create-loyalty-card.dto';
import { IsString, IsOptional, MinLength } from 'class-validator'; // PIN için MinLength gerekebilir
import { Type } from 'class-transformer';

export class UpdateLoyaltyCardDto extends PartialType(CreateLoyaltyCardDto) {
    @IsString()
    @IsOptional()
    @MinLength(4) // PIN için minimum uzunluk (eğer güncelleniyorsa)
    pin?: string;
}
