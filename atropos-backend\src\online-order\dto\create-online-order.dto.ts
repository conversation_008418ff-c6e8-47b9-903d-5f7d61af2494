// src/online-order/dto/create-online-order.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsNumber,
  Min,
  IsBoolean,
  IsEnum,
  IsDateString,
  IsJSON,
  ValidateNested,
  IsArray,
  ArrayMinSize,
  IsEmail,
} from 'class-validator';
import { Type } from 'class-transformer';
// OnlineOrderStatus enum'unu manuel olarak tanımlayalım
enum OnlineOrderStatus {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
  PREPARING = 'PREPARING',
  READY = 'READY',
  DELIVERING = 'DELIVERING',
  DELIVERED = 'DELIVERED',
  CANCELLED = 'CANCELLED',
  RETURNED = 'RETURNED',
}

// Online sipariş kalemi için DTO (OnlineOrder ile iç içe)
class OnlineOrderItemDto {
  @IsString()
  @IsNotEmpty()
  platformProductId: string; // Platformdaki ürün ID'si

  @IsString()
  @IsNotEmpty()
  name: string; // Ürün adı

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 3 })
  @Min(0.001)
  @IsNotEmpty()
  quantity: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsNotEmpty()
  unitPrice: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  totalPrice?: number; // Kalemin toplam fiyatı (platformdan gelen)

  @IsString()
  @IsOptional()
  note?: string; // Müşteri notu
}

export class CreateOnlineOrderDto {
  @IsString()
  @IsNotEmpty()
  platformId: string; // Hangi online platformdan geldiği

  @IsString()
  @IsOptional()
  orderId?: string; // POS sistemindeki karşılık gelen Order ID (manuel bağlantı veya ilk başta boş)

  @IsString()
  @IsNotEmpty()
  platformOrderId: string; // Platformdaki benzersiz sipariş ID'si

  @IsString()
  @IsNotEmpty()
  platformOrderNo: string; // Platformdaki görünen sipariş numarası

  @IsString()
  @IsNotEmpty()
  customerName: string;

  @IsString()
  @IsNotEmpty()
  phone: string;

  @IsString()
  @IsOptional()
  @IsEmail()
  customerEmail?: string;

  @IsString()
  @IsNotEmpty()
  deliveryAddress: string;

  @IsString()
  @IsOptional()
  deliveryNote?: string;

  @IsOptional()
  orderData?: any; // Platformdan gelen ham sipariş verisi (JSON)

  @IsEnum(OnlineOrderStatus)
  @IsOptional()
  status?: OnlineOrderStatus; // Varsayılan: PENDING

  @IsString()
  @IsOptional()
  platformStatus?: string; // Platformdaki durum (örn: "ACCEPTED", "DELIVERED")

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsNotEmpty()
  subtotal: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  deliveryFee?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  serviceFee?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  discount?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsNotEmpty()
  totalAmount: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  commissionAmount?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  netAmount?: number;

  @IsString()
  @IsNotEmpty()
  paymentMethod: string; // Platformdaki ödeme yöntemi

  @IsBoolean()
  @IsOptional()
  isPaid?: boolean;

  @IsDateString()
  @IsNotEmpty()
  orderedAt: Date; // Platformdaki sipariş zamanı

  @IsOptional()
  @IsDateString()
  requestedAt?: Date; // İstenen teslimat zamanı

  // Kabul/Red/Hazırlık vb. zaman damgaları servis tarafından yönetilecek

  @IsString()
  @IsOptional()
  rejectReason?: string;

  @IsString()
  @IsOptional()
  cancelReason?: string;

  // Online sipariş kalemleri
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => OnlineOrderItemDto)
  items: OnlineOrderItemDto[];
}
