// src/recipe/dto/update-recipe.dto.ts
import { PartialType } from '@nestjs/mapped-types';
import { CreateRecipeDto, CreateRecipeItemDto } from './create-recipe.dto';
import {
  IsString,
  IsOptional,
  IsNumber,
  Min,
  IsInt,
  IsBoolean,
  IsArray,
  ValidateNested,
  IsEnum,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';

// ProductUnit enum'unu manuel olarak tanımlayalım
enum ProductUnit {
  PIECE = 'PIECE',
  KG = 'KG',
  GRAM = 'GRAM',
  LITER = 'LITER',
  ML = 'ML',
}

// Güncelleme sırasında reçete kalemi için DTO
export class UpdateRecipeItemDto {
  @IsString()
  @IsOptional()
  id?: string; // Eğer mevcut bir kalemi güncelliyorsak ID'si olur

  @IsString()
  @IsOptional()
  inventoryItemId?: string;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 3 })
  @Min(0.001)
  @IsOptional()
  quantity?: number;

  @IsEnum(ProductUnit)
  @IsOptional()
  unit?: ProductUnit;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  @IsOptional()
  wastagePercent?: number;
}

export class UpdateRecipeDto {
  @IsString()
  @IsOptional()
  productId?: string;

  @IsString()
  @IsOptional()
  name?: string;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 3 })
  @Min(0.001)
  @IsOptional()
  yield?: number;

  @IsString()
  @IsOptional()
  preparationSteps?: string;

  @IsInt()
  @Min(0)
  @IsOptional()
  preparationTime?: number;

  @IsBoolean()
  @IsOptional()
  active?: boolean;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateRecipeItemDto)
  items?: UpdateRecipeItemDto[]; // Reçete kalemlerini güncellemek için
}
