// src/recipe/recipe.service.ts
import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateRecipeDto, CreateRecipeItemDto } from './dto/create-recipe.dto';
import { UpdateRecipeDto, UpdateRecipeItemDto } from './dto/update-recipe.dto';

@Injectable()
export class RecipeService {
  constructor(private prisma: PrismaService) {}

  async createRecipe(data: CreateRecipeDto) {
    // Ürün mevcut mu kontrol et
    const productExists = await this.prisma.product.findUnique({
      where: { id: data.productId, deletedAt: null },
    });
    if (!productExists) {
      throw new NotFoundException(`Product with ID "${data.productId}" not found.`);
    }

    // Bir ürünün sadece bir reçetesi olabilir (Prisma şemasına gö<PERSON> unique([productId]))
    const existingRecipe = await this.prisma.recipe.findUnique({
      where: { productId: data.productId },
    });
    if (existingRecipe) {
      throw new ConflictException(`Recipe already exists for product with ID "${data.productId}".`);
    }

    // Reçete kalemlerini doğrula
    for (const item of data.items) {
      const inventoryItem = await this.prisma.inventoryItem.findUnique({
        where: { id: item.inventoryItemId, deletedAt: null },
      });
      if (!inventoryItem) {
        throw new NotFoundException(`Inventory item with ID "${item.inventoryItemId}" not found.`);
      }
      // Envanter öğesinin birimi ile reçete kaleminin birimi uyumlu mu kontrol et (isteğe bağlı)
      if (inventoryItem.unit !== item.unit) {
        throw new BadRequestException(`Unit mismatch: Inventory item "${inventoryItem.name}" is in ${inventoryItem.unit}, but recipe item uses ${item.unit}.`);
      }
    }

    const recipe = await this.prisma.recipe.create({
      data: {
        ...data,
        yield: parseFloat(data.yield.toFixed(3)),
        items: {
          create: data.items.map(item => ({
            inventoryItemId: item.inventoryItemId,
            quantity: parseFloat(item.quantity.toFixed(3)),
            unit: item.unit,
            wastagePercent: item.wastagePercent !== undefined ? parseFloat(item.wastagePercent.toFixed(2)) : undefined,
          })),
        },
      },
      include: { items: true }, // Oluşturulan reçeteyi ve kalemlerini dön
    });

    return recipe;
  }

  async findAllRecipes(productId?: string) {
    return this.prisma.recipe.findMany({
      where: {
        productId: productId || undefined,
        deletedAt: null,
      },
      include: {
        product: { select: { id: true, name: true, code: true } },
        items: { include: { inventoryItem: { select: { id: true, name: true, unit: true } } } },
      },
      orderBy: { name: 'asc' },
    });
  }

  async findOneRecipe(id: string) {
    const recipe = await this.prisma.recipe.findUnique({
      where: { id, deletedAt: null },
      include: {
        product: { select: { id: true, name: true, code: true } },
        items: { include: { inventoryItem: { select: { id: true, name: true, unit: true } } } },
      },
    });
    if (!recipe) {
      throw new NotFoundException(`Recipe with ID "${id}" not found.`);
    }
    return recipe;
  }

  async updateRecipe(id: string, data: UpdateRecipeDto) {
    const existingRecipe = await this.findOneRecipe(id);

    // Eğer productId güncelleniyorsa, yeni ürünün varlığını ve başka bir reçeteye bağlı olup olmadığını kontrol et
    if (data.productId && data.productId !== existingRecipe.productId) {
        const productExists = await this.prisma.product.findUnique({ where: { id: data.productId, deletedAt: null } });
        if (!productExists) {
            throw new NotFoundException(`Product with ID "${data.productId}" not found.`);
        }
        const existingRecipeForNewProduct = await this.prisma.recipe.findUnique({ where: { productId: data.productId } });
        if (existingRecipeForNewProduct && existingRecipeForNewProduct.id !== id) {
            throw new ConflictException(`Recipe already exists for product with ID "${data.productId}".`);
        }
    }

    const transaction: any[] = [];
    // Reçete kalemleri güncellemelerini yönet
    if (data.items) {
        // Mevcut kalemleri (DB'deki) al
        const currentRecipeItems = await this.prisma.recipeItem.findMany({ where: { recipeId: id } });
        const currentItemIds = new Set(currentRecipeItems.map(item => item.id));
        const incomingItemIds = new Set(data.items.filter(item => item.id).map(item => item.id));

        // Silinecek kalemleri belirle
        const itemsToDelete = currentRecipeItems.filter(item => !incomingItemIds.has(item.id));
        if (itemsToDelete.length > 0) {
            transaction.push(this.prisma.recipeItem.deleteMany({
                where: { id: { in: itemsToDelete.map(item => item.id) } }
            }));
        }

        // Güncellenecek ve eklenecek kalemleri işle
        for (const item of data.items) {
            if (item.id && currentItemIds.has(item.id)) { // Güncelleme
                const updateData: any = {};
                if (item.inventoryItemId) updateData.inventoryItemId = item.inventoryItemId;
                if (item.quantity !== undefined) updateData.quantity = parseFloat(item.quantity.toFixed(3));
                if (item.unit) updateData.unit = item.unit;
                if (item.wastagePercent !== undefined) updateData.wastagePercent = parseFloat(item.wastagePercent.toFixed(2));

                transaction.push(this.prisma.recipeItem.update({
                    where: { id: item.id },
                    data: updateData
                }));
            } else { // Yeni ekleme
                if (!item.inventoryItemId || !item.quantity || !item.unit) {
                    throw new BadRequestException(`Missing required fields for new recipe item.`);
                }

                const inventoryItem = await this.prisma.inventoryItem.findUnique({
                    where: { id: item.inventoryItemId, deletedAt: null },
                });
                if (!inventoryItem) {
                    throw new NotFoundException(`Inventory item with ID "${item.inventoryItemId}" not found for new recipe item.`);
                }
                 if (inventoryItem.unit !== item.unit) {
                    throw new BadRequestException(`Unit mismatch: Inventory item "${inventoryItem.name}" is in ${inventoryItem.unit}, but new recipe item uses ${item.unit}.`);
                }
                transaction.push(this.prisma.recipeItem.create({
                    data: {
                        recipeId: id,
                        inventoryItemId: item.inventoryItemId,
                        quantity: parseFloat(item.quantity.toFixed(3)),
                        unit: item.unit,
                        wastagePercent: item.wastagePercent !== undefined ? parseFloat(item.wastagePercent.toFixed(2)) : 0,
                    }
                }));
            }
        }
    }

    // Ana reçete bilgilerini güncelle
    const updatedRecipeData: any = {
        productId: data.productId,
        name: data.name,
        yield: data.yield !== undefined ? parseFloat(data.yield.toFixed(3)) : undefined,
        preparationSteps: data.preparationSteps,
        preparationTime: data.preparationTime,
        active: data.active,
    };

    // Undefined değerleri temizle
    Object.keys(updatedRecipeData).forEach(key => {
        if (updatedRecipeData[key] === undefined) {
            delete updatedRecipeData[key];
        }
    });

    transaction.push(this.prisma.recipe.update({
        where: { id, deletedAt: null },
        data: updatedRecipeData,
    }));

    try {
        await this.prisma.$transaction(transaction);
        return this.findOneRecipe(id); // Güncellenmiş reçeteyi ilişkileriyle birlikte dön
    } catch (error) {
        if (error.code === 'P2025') {
            throw new NotFoundException(`Recipe with ID "${id}" not found.`);
        }
        throw error;
    }
  }

  async removeRecipe(id: string) {
    // Soft delete uygulaması
    try {
      return await this.prisma.recipe.update({
        where: { id, deletedAt: null },
        data: { deletedAt: new Date(), active: false }, // Soft delete ve pasif hale getir
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Recipe with ID "${id}" not found or already deleted.`);
      }
      throw error;
    }
  }
}
