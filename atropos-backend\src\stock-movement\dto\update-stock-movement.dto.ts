// src/stock-movement/dto/update-stock-movement.dto.ts
import { PartialType } from '@nestjs/mapped-types';
import { CreateStockMovementDto } from './create-stock-movement.dto';
import { IsString, IsOptional, IsDateString } from 'class-validator';

export class UpdateStockMovementDto extends PartialType(CreateStockMovementDto) {
  @IsOptional()
  @IsDateString()
  approvedAt?: Date; // Onaylanma tarihi de güncellenebilir
}
