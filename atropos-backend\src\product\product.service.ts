// src/product/product.service.ts
import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';

@Injectable()
export class ProductService {
  constructor(private prisma: PrismaService) {}

  async createProduct(data: CreateProductDto) {
    // Kategori ve Vergi var mı kontrol et
    const categoryExists = await this.prisma.category.findUnique({
      where: { id: data.categoryId, deletedAt: null },
    });
    if (!categoryExists) {
      throw new NotFoundException(`Category with ID "${data.categoryId}" not found.`);
    }

    const taxExists = await this.prisma.tax.findUnique({
      where: { id: data.taxId, deletedAt: null },
    });
    if (!taxExists) {
      throw new NotFoundException(`Tax with ID "${data.taxId}" not found.`);
    }

    // Aynı şirket içinde aynı SKU (code) ile ürün var mı kontrol et
    const existingProductByCode = await this.prisma.product.findUnique({
      where: {
        companyId_code: {
          companyId: data.companyId,
          code: data.code,
        },
      },
    });
    if (existingProductByCode) {
      throw new ConflictException(`Product with code "${data.code}" already exists for this company.`);
    }

    // Barkod varsa, aynı barkoddan başka ürün var mı kontrol et (Prisma unique kısıtlaması yoksa)
    if (data.barcode) {
      const existingProductByBarcode = await this.prisma.product.findFirst({
        where: { barcode: data.barcode, deletedAt: null },
      });
      if (existingProductByBarcode) {
        throw new ConflictException(`Product with barcode "${data.barcode}" already exists.`);
      }
    }

    return this.prisma.product.create({
      data: {
        ...data,
        basePrice: parseFloat(data.basePrice.toFixed(2)),
        costPrice: data.costPrice !== undefined ? parseFloat(data.costPrice.toFixed(2)) : undefined,
        profitMargin: data.profitMargin !== undefined ? parseFloat(data.profitMargin.toFixed(2)) : undefined,
        criticalStock: data.criticalStock !== undefined ? parseFloat(data.criticalStock.toFixed(3)) : undefined,
      },
    });
  }

  async findAllProducts(companyId?: string, categoryId?: string) {
    return this.prisma.product.findMany({
      where: {
        companyId: companyId || undefined,
        categoryId: categoryId || undefined,
        deletedAt: null,
      },
      include: {
        category: { select: { id: true, name: true } },
        tax: { select: { id: true, name: true, rate: true } },
      },
      orderBy: { name: 'asc' },
    });
  }

  async findOneProduct(id: string) {
    const product = await this.prisma.product.findUnique({
      where: { id, deletedAt: null },
      include: {
        category: { select: { id: true, name: true } },
        tax: { select: { id: true, name: true, rate: true } },
        variants: true,
        modifierGroups: true,
        recipes: true,
      },
    });
    if (!product) {
      throw new NotFoundException(`Product with ID "${id}" not found.`);
    }
    return product;
  }

  async updateProduct(id: string, data: UpdateProductDto) {
    // Kategori veya Vergi güncelleniyorsa varlıklarını kontrol et
    if (data.categoryId) {
      const categoryExists = await this.prisma.category.findUnique({
        where: { id: data.categoryId, deletedAt: null },
      });
      if (!categoryExists) {
        throw new NotFoundException(`Category with ID "${data.categoryId}" not found.`);
      }
    }
    if (data.taxId) {
      const taxExists = await this.prisma.tax.findUnique({
        where: { id: data.taxId, deletedAt: null },
      });
      if (!taxExists) {
        throw new NotFoundException(`Tax with ID "${data.taxId}" not found.`);
      }
    }

    // Code güncelleniyorsa ve başka bir şirkette veya kendi şirketinde aynı code varsa kontrol et
    if (data.code) {
      const currentProduct = await this.findOneProduct(id);
      const existingProductByCode = await this.prisma.product.findUnique({
        where: {
          companyId_code: {
            companyId: data.companyId || currentProduct.companyId,
            code: data.code,
          },
        },
      });
      if (existingProductByCode && existingProductByCode.id !== id) {
        throw new ConflictException(`Product with code "${data.code}" already exists for this company.`);
      }
    }

    // Barkod güncelleniyorsa ve başka bir üründe kullanılıyorsa kontrol et
    if (data.barcode) {
      const existingProductByBarcode = await this.prisma.product.findFirst({
        where: { barcode: data.barcode, id: { not: id }, deletedAt: null },
      });
      if (existingProductByBarcode) {
        throw new ConflictException(`Product with barcode "${data.barcode}" already exists for another product.`);
      }
    }

    try {
      return await this.prisma.product.update({
        where: { id, deletedAt: null },
        data: {
            ...data,
            basePrice: data.basePrice !== undefined ? parseFloat(data.basePrice.toFixed(2)) : undefined,
            costPrice: data.costPrice !== undefined ? parseFloat(data.costPrice.toFixed(2)) : undefined,
            profitMargin: data.profitMargin !== undefined ? parseFloat(data.profitMargin.toFixed(2)) : undefined,
            criticalStock: data.criticalStock !== undefined ? parseFloat(data.criticalStock.toFixed(3)) : undefined,
        },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Product with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removeProduct(id: string) {
    // Bu ürüne bağlı OrderItem, ProductVariant, ModifierGroup, Recipe gibi ilişkiler var mı kontrol et
    // (Bu kontrol şimdilik en temel seviyede, tüm ilişkiler için genişletilebilir)
    const orderItemsCount = await this.prisma.orderItem.count({
        where: { productId: id }
    });
    if (orderItemsCount > 0) {
        throw new ConflictException(`Product with ID "${id}" cannot be deleted because it has ${orderItemsCount} associated order items.`);
    }

    const variantsCount = await this.prisma.productVariant.count({
        where: { productId: id }
    });
    if (variantsCount > 0) {
        throw new ConflictException(`Product with ID "${id}" cannot be deleted because it has ${variantsCount} associated variants.`);
    }
    // ... diğer ilişkiler için de benzer kontroller eklenebilir (modifierGroups, recipes, comboItems, onlineProductMappings)

    // Soft delete uygulaması
    try {
      return await this.prisma.product.update({
        where: { id, deletedAt: null },
        data: { deletedAt: new Date(), active: false, sellable: false, available: false, showInMenu: false, featured: false }, // Soft delete ve pasif hale getirme
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Product with ID "${id}" not found or already deleted.`);
      }
      throw error;
    }
  }
}
