// src/courier-location/dto/create-courier-location.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  Min,
  Max,
  IsDateString,
  IsOptional,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateCourierLocationDto {
  @IsString()
  @IsNotEmpty()
  courierId: string; // Kurye kullanıcı ID'si

  @IsString()
  @IsNotEmpty()
  branchId: string; // Kuryenin bağlı olduğu şube ID'si

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 6 }) // Enlem/<PERSON><PERSON> hassasiyeti
  @Min(-90)
  @Max(90)
  @IsNotEmpty()
  latitude: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 6 }) // Enlem/Boylam hassasiyeti
  @Min(-180)
  @Max(180)
  @IsNotEmpty()
  longitude: number;

  @IsOptional()
  @IsDateString()
  timestamp?: Date; // <PERSON><PERSON><PERSON> kaydedil<PERSON>ği zaman (varsayılan now())

  // expiresAt alanı servis tarafından otomatik atanacak
}
