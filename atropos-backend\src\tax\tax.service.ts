// src/tax/tax.service.ts
import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateTaxDto } from './dto/create-tax.dto';
import { UpdateTaxDto } from './dto/update-tax.dto';

@Injectable()
export class TaxService {
  constructor(private prisma: PrismaService) {}

  async createTax(data: CreateTaxDto) {
    // Aynı şirket içinde aynı kodda vergi var mı kontrol et
    const existingTax = await this.prisma.tax.findUnique({
      where: {
        companyId_code: {
          companyId: data.companyId,
          code: data.code,
        },
      },
    });

    if (existingTax) {
      throw new ConflictException(`Tax with code "${data.code}" already exists for this company.`);
    }

    return this.prisma.tax.create({ data: {
        ...data,
        rate: parseFloat(data.rate.toFixed(2)), // Decimal precision'ı korumak için
    }});
  }

  async findAllTaxes(companyId?: string) {
    return this.prisma.tax.findMany({
      where: { companyId: companyId || undefined, deletedAt: null },
      include: { company: { select: { id: true, name: true } } },
    });
  }

  async findOneTax(id: string) {
    const tax = await this.prisma.tax.findUnique({
      where: { id, deletedAt: null },
      include: { company: { select: { id: true, name: true } } },
    });
    if (!tax) {
      throw new NotFoundException(`Tax with ID "${id}" not found.`);
    }
    return tax;
  }

  async updateTax(id: string, data: UpdateTaxDto) {
    // Eğer code değişiyorsa unique kontrolü
    if (data.code) {
        const currentTax = await this.findOneTax(id);
        const existingTax = await this.prisma.tax.findUnique({
            where: {
                companyId_code: {
                    companyId: data.companyId || currentTax.companyId,
                    code: data.code,
                },
            },
        });
        if (existingTax && existingTax.id !== id) {
            throw new ConflictException(`Tax with code "${data.code}" already exists for this company.`);
        }
    }
    
    try {
      return await this.prisma.tax.update({
        where: { id, deletedAt: null },
        data: {
            ...data,
            rate: data.rate !== undefined ? parseFloat(data.rate.toFixed(2)) : undefined,
        },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Tax with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removeTax(id: string) {
    // Bu vergiye bağlı ürün var mı kontrol et
    const productsCount = await this.prisma.product.count({
        where: { taxId: id, deletedAt: null }
    });

    if (productsCount > 0) {
        throw new ConflictException(`Tax with ID "${id}" cannot be deleted because it has ${productsCount} active products.`);
    }

    // Soft delete uygulaması
    try {
      return await this.prisma.tax.update({
        where: { id, deletedAt: null },
        data: { deletedAt: new Date(), active: false },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Tax with ID "${id}" not found or already deleted.`);
      }
      throw error;
    }
  }
}
