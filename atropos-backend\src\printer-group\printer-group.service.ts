// src/printer-group/printer-group.service.ts
import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreatePrinterGroupDto } from './dto/create-printer-group.dto';
import { UpdatePrinterGroupDto } from './dto/update-printer-group.dto';

@Injectable()
export class PrinterGroupService {
  constructor(private prisma: PrismaService) {}

  async createPrinterGroup(data: CreatePrinterGroupDto) {
    // Name benzersiz mi kontrol et
    const existingGroup = await this.prisma.printerGroup.findUnique({
      where: { name: data.name },
    });
    if (existingGroup) {
      throw new ConflictException(`Printer group with name "${data.name}" already exists.`);
    }

    // categoryIds varsa, kategorilerin mevcut olduğunu kontrol et ve ilişkileri kur
    if (data.categoryIds && data.categoryIds.length > 0) {
        const categories = await this.prisma.category.findMany({
            where: { id: { in: data.categoryIds }, deletedAt: null },
        });
        if (categories.length !== data.categoryIds.length) {
            const foundIds = categories.map(c => c.id);
            const notFoundIds = data.categoryIds.filter(id => !foundIds.includes(id));
            throw new NotFoundException(`Some categories not found: ${notFoundIds.join(', ')}.`);
        }
    }
    
    return this.prisma.printerGroup.create({
      data: {
        name: data.name,
        categories: {
            connect: data.categoryIds?.map(id => ({ id })) || []
        }
      },
      include: { categories: { select: { id: true, name: true } } }
    });
  }

  async findAllPrinterGroups() {
    return this.prisma.printerGroup.findMany({
      include: { categories: { select: { id: true, name: true } }, printers: true },
      orderBy: { name: 'asc' },
    });
  }

  async findOnePrinterGroup(id: string) {
    const group = await this.prisma.printerGroup.findUnique({
      where: { id },
      include: { categories: { select: { id: true, name: true } }, printers: true },
    });
    if (!group) {
      throw new NotFoundException(`Printer group with ID "${id}" not found.`);
    }
    return group;
  }

  async updatePrinterGroup(id: string, data: UpdatePrinterGroupDto) {
    const existingGroup = await this.findOnePrinterGroup(id);

    // Name güncelleniyorsa benzersiz mi kontrol et
    if (data.name && data.name !== existingGroup.name) {
      const existingGroupByName = await this.prisma.printerGroup.findUnique({
        where: { name: data.name },
      });
      if (existingGroupByName && existingGroupByName.id !== id) {
        throw new ConflictException(`Printer group with name "${data.name}" already exists.`);
      }
    }

    // categoryIds güncelleniyorsa
    if (data.categoryIds !== undefined) {
        const categories = await this.prisma.category.findMany({
            where: { id: { in: data.categoryIds }, deletedAt: null },
        });
        if (categories.length !== data.categoryIds.length) {
            const foundIds = categories.map(c => c.id);
            const notFoundIds = data.categoryIds.filter(id => !foundIds.includes(id));
            throw new NotFoundException(`Some categories not found: ${notFoundIds.join(', ')}.`);
        }
        // Mevcut tüm bağlantıları kopar, yeni bağlantıları kur
        // Bu, Many-to-Many ilişkilerde ID'leri yönetirken basit bir "yeniden bağlantı" stratejisidir.
        // Daha akıllıca, sadece eklenenleri ekleyip, çıkarılanları çıkarmak da mümkündür.
        await this.prisma.printerGroup.update({
            where: { id },
            data: {
                categories: {
                    set: data.categoryIds.map(categoryId => ({ id: categoryId }))
                }
            }
        });
    }

    try {
      return await this.prisma.printerGroup.update({
        where: { id },
        data: {
            name: data.name, // Sadece name'i güncelle
            // categories ilişkisi yukarıda manuel olarak yönetildi
        },
        include: { categories: { select: { id: true, name: true } } }
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Printer group with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removePrinterGroup(id: string) {
    // Bu yazıcı grubuna bağlı Printer veya Category var mı kontrol et
    const printersCount = await this.prisma.printer.count({
        where: { printerGroupId: id }
    });
    if (printersCount > 0) {
        throw new ConflictException(`Printer group with ID "${id}" cannot be deleted because it has ${printersCount} associated printers.`);
    }

    const categoriesCount = await this.prisma.category.count({
        where: { printerGroupId: id, deletedAt: null }
    });
    if (categoriesCount > 0) {
        throw new ConflictException(`Printer group with ID "${id}" cannot be deleted because it is assigned to ${categoriesCount} active categories.`);
    }

    // PrinterGroup modelinde deletedAt alanı yok, bu nedenle fiziksel silme yapıyoruz.
    try {
      return await this.prisma.printerGroup.delete({
        where: { id },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Printer group with ID "${id}" not found.`);
      }
      throw error;
    }
  }
}
