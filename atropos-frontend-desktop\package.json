{"name": "atropos-frontend-desktop", "private": true, "version": "0.0.0", "type": "module", "main": "dist-electron/main.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:vite\" \"npm run dev:electron\"", "dev:vite": "vite --port 5173", "dev:electron": "wait-on http-get://localhost:5173 && tsc -p electron/tsconfig.json && copy electron\\preload.mjs dist-electron\\preload.mjs && set NODE_ENV=development&& set VITE_DEV_SERVER_URL=http://localhost:5173&& electron .", "build": "vite build && tsc -p electron/tsconfig.json && copy electron\\preload.mjs dist-electron\\preload.mjs && electron-builder", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fluentui/react-components": "^9.67.0", "@fluentui/react-icons": "^2.0.306", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "concurrently": "^9.2.0", "electron": "^37.2.4", "electron-builder": "^26.0.12", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4", "wait-on": "^8.0.4"}}