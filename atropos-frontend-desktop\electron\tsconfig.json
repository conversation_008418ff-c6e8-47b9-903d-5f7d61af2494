// electron/tsconfig.json
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ES2022", // ESM kullan
    "moduleResolution": "node",
    "outDir": "../dist-electron", // Derlenmiş JS dosyalarının kaydedileceği dizin
    "strict": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": [
    "main.ts",
    "preload.mjs"
  ]
}