// src/table-merge/table-merge.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { TableMergeService } from './table-merge.service';
import { CreateTableMergeDto } from './dto/create-table-merge.dto';
import { UpdateTableMergeDto } from './dto/update-table-merge.dto';

@Controller('table-merge')
export class TableMergeController {
  constructor(private readonly tableMergeService: TableMergeService) {}

  @Post() // POST /table-merge
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createTableMergeDto: CreateTableMergeDto) {
    return this.tableMergeService.createTableMerge(createTableMergeDto);
  }

  @Get() // GET /table-merge?tableId=...&targetId=...
  findAll(
    @Query('tableId') tableId?: string,
    @Query('targetId') targetId?: string,
  ) {
    return this.tableMergeService.findAllTableMerges(tableId, targetId);
  }

  @Get(':id') // GET /table-merge/:id
  findOne(@Param('id') id: string) {
    return this.tableMergeService.findOneTableMerge(id);
  }

  @Patch(':id') // PATCH /table-merge/:id
  update(@Param('id') id: string, @Body() updateTableMergeDto: UpdateTableMergeDto) {
    return this.tableMergeService.updateTableMerge(id, updateTableMergeDto);
  }

  @Delete(':id') // DELETE /table-merge/:id (Fiziksel silme - ayırma)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.tableMergeService.removeTableMerge(id);
  }
}
