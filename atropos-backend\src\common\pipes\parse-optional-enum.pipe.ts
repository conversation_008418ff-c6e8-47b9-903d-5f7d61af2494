// src/common/pipes/parse-optional-enum.pipe.ts
import { PipeTransform, Injectable, ArgumentMetadata, BadRequestException } from '@nestjs/common';

@Injectable()
export class ParseOptionalEnumPipe implements PipeTransform {
  constructor(private readonly enumObject: any) {}

  transform(value: string, metadata: ArgumentMetadata): any {
    if (!value) {
      return undefined;
    }
    
    const enumValues = Object.values(this.enumObject);
    if (!enumValues.includes(value)) {
      throw new BadRequestException(`Validation failed (${enumValues.join(', ')} expected for ${metadata.data}).`);
    }
    
    return value;
  }
}
