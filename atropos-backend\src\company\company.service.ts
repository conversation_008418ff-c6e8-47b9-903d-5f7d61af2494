// src/company/company.service.ts
import { Injectable, NotFoundException, ConflictException, Logger } from '@nestjs/common'; // Logger'ı import et
import { PrismaService } from '../prisma/prisma.service';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';

@Injectable()
export class CompanyService {
  private readonly logger = new Logger(CompanyService.name); // Class adını context olarak ver

  constructor(private prisma: PrismaService) {}

  async createCompany(data: CreateCompanyDto) {
    this.logger.log(`Attempting to create a new company: ${data.name}`); // Bilgi mesajı
    try {
      // TaxNumber unique olarak işaretlendiği için kontrol et
      const existingCompany = await this.prisma.company.findUnique({
        where: { taxNumber: data.taxNumber },
      });
      if (existingCompany) {
        this.logger.warn(`Company creation failed: Tax number ${data.taxNumber} already exists.`); // <PERSON>yarı mesajı
        throw new ConflictException(`Company with tax number "${data.taxNumber}" already exists.`);
      }
      const company = await this.prisma.company.create({ data });
      this.logger.log(`Company "${company.name}" created successfully with ID: ${company.id}`); // Başarı mesajı
      return company;
    } catch (error) {
      this.logger.error(`Error creating company "${data.name}": ${error.message}`, error.stack); // Hata mesajı
      throw error;
    }
  }

  async findAllCompanies() {
    this.logger.verbose('Fetching all companies from the database.'); // Detaylı bilgi
    return this.prisma.company.findMany();
  }

  async findOneCompany(id: string) {
    this.logger.debug(`Attempting to find company with ID: ${id}`); // Debug mesajı
    const company = await this.prisma.company.findUnique({
      where: { id },
    });
    if (!company) {
      this.logger.warn(`Company with ID "${id}" not found.`);
      throw new NotFoundException(`Company with ID "${id}" not found.`);
    }
    return company;
  }

  async updateCompany(id: string, data: UpdateCompanyDto) {
    this.logger.log(`Attempting to update company with ID: ${id}`);
    try {
      const company = await this.prisma.company.update({
        where: { id },
        data,
      });
      this.logger.log(`Company with ID "${id}" updated successfully`);
      return company;
    } catch (error) {
      if (error.code === 'P2025') { // Prisma'da kayıt bulunamadı hatası kodu
        this.logger.warn(`Update failed: Company with ID "${id}" not found.`);
        throw new NotFoundException(`Company with ID "${id}" not found.`);
      }
      this.logger.error(`Error updating company with ID "${id}": ${error.message}`, error.stack);
      throw error; // Diğer hataları yeniden fırlat
    }
  }

  async removeCompany(id: string) {
    this.logger.log(`Attempting to delete company with ID: ${id}`);
    try {
      const company = await this.prisma.company.delete({
        where: { id },
      });
      this.logger.log(`Company with ID "${id}" deleted successfully`);
      return company;
    } catch (error) {
      if (error.code === 'P2025') { // Prisma'da kayıt bulunamadı hatası kodu
        this.logger.warn(`Delete failed: Company with ID "${id}" not found.`);
        throw new NotFoundException(`Company with ID "${id}" not found.`);
      }
      this.logger.error(`Error deleting company with ID "${id}": ${error.message}`, error.stack);
      throw error; // Diğer hataları yeniden fırlat
    }
  }
}