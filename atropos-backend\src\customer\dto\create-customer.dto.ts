// src/customer/dto/create-customer.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEmail,
  IsBoolean,
  IsInt,
  Min,
  Max,
  IsArray,
  IsDateString,
  IsNumber,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateCustomerDto {
  @IsString()
  @IsOptional()
  firstName?: string;

  @IsString()
  @IsOptional()
  lastName?: string;

  @IsString()
  @IsOptional()
  companyName?: string;

  @IsString()
  @IsOptional()
  title?: string; // "Dr.", "Prof."

  @IsString()
  @IsOptional()
  taxNumber?: string;

  @IsString()
  @IsOptional()
  taxOffice?: string;

  @IsString()
  @IsNotEmpty()
  phone: string; // Benzersiz olmalı

  @IsString()
  @IsOptional()
  phone2?: string;

  @IsString()
  @IsOptional()
  @IsEmail()
  email?: string;

  @IsString()
  @IsOptional()
  address?: string;

  @IsString()
  @IsOptional()
  district?: string;

  @IsString()
  @IsOptional()
  city?: string;

  @IsString()
  @IsOptional()
  country?: string;

  @IsString()
  @IsOptional()
  postalCode?: string;

  @IsOptional()
  @IsDateString()
  birthDate?: Date;

  @IsString()
  @IsOptional()
  gender?: string; // "M", "F", "O"

  @IsBoolean()
  @IsOptional()
  marketingConsent?: boolean;

  @IsBoolean()
  @IsOptional()
  smsConsent?: boolean;

  @IsBoolean()
  @IsOptional()
  emailConsent?: boolean;

  @IsInt()
  @IsOptional()
  @Min(0)
  loyaltyPoints?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  totalSpent?: number;

  @IsInt()
  @IsOptional()
  @Min(0)
  orderCount?: number;

  @IsOptional()
  @IsDateString()
  lastOrderDate?: Date;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  currentDebt?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  creditLimit?: number;

  @IsInt()
  @IsOptional()
  @Min(0)
  paymentTerm?: number;

  @IsString()
  @IsOptional()
  segment?: string; // "VIP", "REGULAR", "NEW"

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];

  // customFields Json?: DTO'da doğrudan validasyonu zor olabilir, genellikle any veya Record<string, any> olarak alınır

  @IsString()
  @IsOptional()
  notes?: string;

  @IsString()
  @IsOptional()
  source?: string; // "POS", "ONLINE", "IMPORT"

  @IsString()
  @IsOptional()
  referredBy?: string;

  @IsBoolean()
  @IsOptional()
  blacklisted?: boolean;

  @IsString()
  @IsOptional()
  blacklistReason?: string;

  @IsBoolean()
  @IsOptional()
  active?: boolean;
}
