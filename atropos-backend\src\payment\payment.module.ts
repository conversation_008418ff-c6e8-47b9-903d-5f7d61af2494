// src/payment/payment.module.ts
import { Module } from '@nestjs/common';
import { PaymentService } from './payment.service';
import { PaymentController } from './payment.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { OrderModule } from '../order/order.module'; // Sipariş servisini kullanmak gerekirse

@Module({
  imports: [PrismaModule, OrderModule], // OrderService'e bağımlı olunabilir (ancak doğrudan kullanılmıyor, PrismaService üzerinden gidiliyor)
  controllers: [PaymentController],
  providers: [PaymentService],
  exports: [PaymentService],
})
export class PaymentModule {}
