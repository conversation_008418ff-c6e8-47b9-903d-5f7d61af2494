// src/daily-report/dto/update-daily-report.dto.ts
import { PartialType } from '@nestjs/mapped-types';
import { CreateDailyReportDto } from './create-daily-report.dto';
import { IsString, IsOptional, IsDateString } from 'class-validator';

export class UpdateDailyReportDto extends PartialType(CreateDailyReportDto) {
  @IsString()
  @IsOptional()
  approvedBy?: string;

  @IsOptional()
  @IsDateString()
  approvedAt?: Date;

  @IsOptional()
  @IsDateString()
  printedAt?: Date;

  @IsOptional()
  @IsDateString()
  emailedAt?: Date;
}
