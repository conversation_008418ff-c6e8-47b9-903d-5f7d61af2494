// src/category/category.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { CategoryService } from './category.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';

@Controller('category')
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @Post() // POST /category
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createCategoryDto: CreateCategoryDto) {
    return this.categoryService.createCategory(createCategoryDto);
  }

  @Get() // GET /category?companyId=...&parentId=...
  findAll(
    @Query('companyId') companyId?: string,
    @Query('parentId') parentId?: string, // Ana kategorileri getirmek için null gönderilebilir
  ) {
    return this.categoryService.findAllCategories(companyId, parentId);
  }

  @Get(':id') // GET /category/:id
  findOne(@Param('id') id: string) {
    return this.categoryService.findOneCategory(id);
  }

  @Patch(':id') // PATCH /category/:id
  update(@Param('id') id: string, @Body() updateCategoryDto: UpdateCategoryDto) {
    return this.categoryService.updateCategory(id, updateCategoryDto);
  }

  @Delete(':id') // DELETE /category/:id
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.categoryService.removeCategory(id);
  }
}
