# Backend API Configuration
VITE_API_URL="http://localhost:3000"

# Application Configuration
VITE_APP_NAME="Atropos POS"
VITE_APP_VERSION="1.0.0"

# Development Configuration
VITE_DEV_SERVER_URL="http://localhost:5173"

# Electron Configuration
VITE_ELECTRON_MAIN_ENTRY="dist-electron/main.js"
VITE_ELECTRON_PRELOAD_ENTRY="dist-electron/preload.mjs"

# Build Configuration
VITE_BUILD_TARGET="electron"
VITE_OUTPUT_DIR="dist"

# Security Configuration
VITE_ENABLE_DEV_TOOLS=true

# Logging Configuration
VITE_LOG_LEVEL="debug"

# Auto-updater Configuration (Optional)
VITE_AUTO_UPDATER_ENABLED=false
VITE_UPDATE_SERVER_URL=""

# Analytics Configuration (Optional)
VITE_ANALYTICS_ENABLED=false
VITE_ANALYTICS_ID=""

# Feature Flags
VITE_ENABLE_OFFLINE_MODE=true
VITE_ENABLE_DARK_MODE=true
VITE_ENABLE_NOTIFICATIONS=true
