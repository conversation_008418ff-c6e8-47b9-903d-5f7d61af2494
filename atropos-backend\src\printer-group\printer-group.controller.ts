// src/printer-group/printer-group.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { PrinterGroupService } from './printer-group.service';
import { CreatePrinterGroupDto } from './dto/create-printer-group.dto';
import { UpdatePrinterGroupDto } from './dto/update-printer-group.dto';

@Controller('printer-group')
export class PrinterGroupController {
  constructor(private readonly printerGroupService: PrinterGroupService) {}

  @Post() // POST /printer-group
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createPrinterGroupDto: CreatePrinterGroupDto) {
    return this.printerGroupService.createPrinterGroup(createPrinterGroupDto);
  }

  @Get() // GET /printer-group
  findAll() {
    return this.printerGroupService.findAllPrinterGroups();
  }

  @Get(':id') // GET /printer-group/:id
  findOne(@Param('id') id: string) {
    return this.printerGroupService.findOnePrinterGroup(id);
  }

  @Patch(':id') // PATCH /printer-group/:id
  update(@Param('id') id: string, @Body() updatePrinterGroupDto: UpdatePrinterGroupDto) {
    return this.printerGroupService.updatePrinterGroup(id, updatePrinterGroupDto);
  }

  @Delete(':id') // DELETE /printer-group/:id (Fiziksel silme)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.printerGroupService.removePrinterGroup(id);
  }
}
