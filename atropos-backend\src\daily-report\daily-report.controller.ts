// src/daily-report/daily-report.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { DailyReportService } from './daily-report.service';
import { CreateDailyReportDto } from './dto/create-daily-report.dto';
import { UpdateDailyReportDto } from './dto/update-daily-report.dto';
import { ParseOptionalDatePipe } from '../common/pipes/parse-optional-date.pipe';

@Controller('daily-report')
export class DailyReportController {
  constructor(private readonly dailyReportService: DailyReportService) {}

  @Post() // POST /daily-report
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createDailyReportDto: CreateDailyReportDto) {
    return this.dailyReportService.createDailyReport(createDailyReportDto);
  }

  @Get() // GET /daily-report?branchId=...&startDate=...&endDate=...
  findAll(
    @Query('branchId') branchId?: string,
    @Query('startDate', ParseOptionalDatePipe) startDate?: Date,
    @Query('endDate', ParseOptionalDatePipe) endDate?: Date,
  ) {
    return this.dailyReportService.findAllDailyReports(branchId, startDate, endDate);
  }

  @Get(':id') // GET /daily-report/:id
  findOne(@Param('id') id: string) {
    return this.dailyReportService.findOneDailyReport(id);
  }

  @Patch(':id') // PATCH /daily-report/:id
  update(@Param('id') id: string, @Body() updateDailyReportDto: UpdateDailyReportDto) {
    return this.dailyReportService.updateDailyReport(id, updateDailyReportDto);
  }

  @Delete(':id') // DELETE /daily-report/:id (Fiziksel silme)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.dailyReportService.removeDailyReport(id);
  }
}
