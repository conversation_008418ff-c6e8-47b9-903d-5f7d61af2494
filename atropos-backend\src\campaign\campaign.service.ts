// src/campaign/campaign.service.ts
import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCampaignDto } from './dto/create-campaign.dto';
import { UpdateCampaignDto } from './dto/update-campaign.dto';
import { CampaignType } from '../../generated/prisma';

@Injectable()
export class CampaignService {
  constructor(private prisma: PrismaService) {}

  async createCampaign(data: CreateCampaignDto) {
    // Şirket mevcut mu kontrol et
    const companyExists = await this.prisma.company.findUnique({
      where: { id: data.companyId, deletedAt: null },
    });
    if (!companyExists) {
      throw new NotFoundException(`Company with ID "${data.companyId}" not found.`);
    }

    // Aynı şirket içinde aynı kodda kampanya var mı kontrol et
    const existingCampaign = await this.prisma.campaign.findUnique({
      where: {
        companyId_code: {
          companyId: data.companyId,
          code: data.code,
        },
      },
    });
    if (existingCampaign) {
      throw new ConflictException(`Campaign with code "${data.code}" already exists for this company.`);
    }

    // Kampanya tipi ve indirim tipi/değeri uyumluluğu kontrolü
    if (data.campaignType === CampaignType.DISCOUNT) {
        if (!data.discountType || data.discountValue === undefined) {
            throw new BadRequestException('discountType and discountValue are required for DISCOUNT campaigns.');
        }
    } else {
        // DISCOUNT olmayan kampanyalarda bu alanlar gönderilmemeli (opsiyonel olarak)
        if (data.discountType !== undefined || data.discountValue !== undefined) {
             throw new BadRequestException('discountType and discountValue should not be provided for non-DISCOUNT campaigns.');
        }
    }

    return this.prisma.campaign.create({
      data: {
        ...data,
        discountValue: data.discountValue !== undefined ? parseFloat(data.discountValue.toFixed(2)) : undefined,
        minOrderAmount: data.minOrderAmount !== undefined ? parseFloat(data.minOrderAmount.toFixed(2)) : undefined,
        maxDiscountAmount: data.maxDiscountAmount !== undefined ? parseFloat(data.maxDiscountAmount.toFixed(2)) : undefined,
        startDate: new Date(data.startDate),
        endDate: data.endDate ? new Date(data.endDate) : undefined,
      },
    });
  }

  async findAllCampaigns(companyId?: string, campaignType?: CampaignType, active?: boolean) {
    return this.prisma.campaign.findMany({
      where: {
        companyId: companyId || undefined,
        campaignType: campaignType || undefined,
        active: active !== undefined ? active : undefined,
      },
      include: { company: { select: { id: true, name: true } } },
      orderBy: { startDate: 'desc' },
    });
  }

  async findOneCampaign(id: string) {
    const campaign = await this.prisma.campaign.findUnique({
      where: { id },
      include: { company: { select: { id: true, name: true } } },
    });
    if (!campaign) {
      throw new NotFoundException(`Campaign with ID "${id}" not found.`);
    }
    return campaign;
  }

  async updateCampaign(id: string, data: UpdateCampaignDto) {
    const existingCampaign = await this.findOneCampaign(id);

    // Eğer companyId güncelleniyorsa, yeni şirketin mevcut olduğunu doğrula
    if (data.companyId && data.companyId !== existingCampaign.companyId) {
        const companyExists = await this.prisma.company.findUnique({ where: { id: data.companyId, deletedAt: null } });
        if (!companyExists) { throw new NotFoundException(`Company with ID "${data.companyId}" not found.`); }
    }

    // Code güncelleniyorsa unique kontrolü
    if (data.code && data.code !== existingCampaign.code) {
        const existingCampaignByCode = await this.prisma.campaign.findUnique({
            where: {
                companyId_code: {
                    companyId: data.companyId || existingCampaign.companyId,
                    code: data.code,
                },
            },
        });
        if (existingCampaignByCode && existingCampaignByCode.id !== id) {
            throw new ConflictException(`Campaign with code "${data.code}" already exists for this company.`);
        }
    }

    // Kampanya tipi değiştiriliyorsa veya indirim alanları güncelleniyorsa kontrol et
    const targetCampaignType = data.campaignType || existingCampaign.campaignType;
    if (targetCampaignType === CampaignType.DISCOUNT) {
        if (data.discountType === undefined && existingCampaign.discountType === undefined && data.discountValue === undefined && existingCampaign.discountValue === undefined) {
            // Eğer yeni bir DISCOUNT tipine geçiliyorsa ve değerler yoksa hata ver
            if (data.campaignType === CampaignType.DISCOUNT && existingCampaign.campaignType !== CampaignType.DISCOUNT) {
                throw new BadRequestException('discountType and discountValue are required when changing to DISCOUNT campaign type.');
            }
        }
    } else {
        // DISCOUNT olmayan kampanyalarda bu alanlar gönderilmemeli
        if (data.discountType !== undefined || data.discountValue !== undefined) {
             throw new BadRequestException('discountType and discountValue should not be provided for non-DISCOUNT campaigns.');
        }
    }

    try {
      return await this.prisma.campaign.update({
        where: { id },
        data: {
            ...data,
            discountValue: data.discountValue !== undefined ? parseFloat(data.discountValue.toFixed(2)) : undefined,
            minOrderAmount: data.minOrderAmount !== undefined ? parseFloat(data.minOrderAmount.toFixed(2)) : undefined,
            maxDiscountAmount: data.maxDiscountAmount !== undefined ? parseFloat(data.maxDiscountAmount.toFixed(2)) : undefined,
            startDate: data.startDate ? new Date(data.startDate) : undefined,
            endDate: data.endDate ? new Date(data.endDate) : undefined,
        },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Campaign with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removeCampaign(id: string) {
    // Bu kampanyaya bağlı CampaignUsage var mı kontrol et
    const usagesCount = await this.prisma.campaignUsage.count({
        where: { campaignId: id }
    });
    if (usagesCount > 0) {
        throw new ConflictException(`Campaign with ID "${id}" cannot be deleted because it has ${usagesCount} associated usages.`);
    }

    // Campaign modelinde deletedAt alanı yok.
    // Genellikle kampanyalar fiziksel olarak silinmez, "active: false" yapılır ve "endDate" belirlenir.
    // Bu metod sadece kartı pasif hale getirecek.
    try {
      return await this.prisma.campaign.update({
        where: { id },
        data: { active: false }, // Pasif hale getir
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Campaign with ID "${id}" not found.`);
      }
      throw error;
    }
  }
}
