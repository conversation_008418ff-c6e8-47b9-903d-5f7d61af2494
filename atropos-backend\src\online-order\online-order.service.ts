// src/online-order/online-order.service.ts
import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateOnlineOrderDto } from './dto/create-online-order.dto';
import { UpdateOnlineOrderDto } from './dto/update-online-order.dto';
// Enum'ları manuel olarak tanımlayalım
enum OnlineOrderStatus {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
  PREPARING = 'PREPARING',
  READY = 'READY',
  DELIVERING = 'DELIVERING',
  DELIVERED = 'DELIVERED',
  CANCELLED = 'CANCELLED',
  RETURNED = 'RETURNED',
}

enum OrderStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  PREPARING = 'PREPARING',
  READY = 'READY',
  DELIVERED = 'DELIVERED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
}

@Injectable()
export class OnlineOrderService {
  constructor(private prisma: PrismaService) {}

  async createOnlineOrder(data: CreateOnlineOrderDto) {
    // OnlinePlatform mevcut mu kontrol et
    const platform = await this.prisma.onlinePlatform.findUnique({
      where: { id: data.platformId },
    });
    if (!platform) {
      throw new NotFoundException(`Online platform with ID "${data.platformId}" not found.`);
    }

    // Aynı platformda, aynı platform sipariş ID'si zaten var mı kontrol et
    const existingOnlineOrder = await this.prisma.onlineOrder.findUnique({
      where: {
        platformId_platformOrderId: {
          platformId: data.platformId,
          platformOrderId: data.platformOrderId,
        },
      },
    });
    if (existingOnlineOrder) {
      throw new ConflictException(`Online order with platform order ID "${data.platformOrderId}" already exists for this platform.`);
    }

    // Eğer orderId belirtilmişse, kendi POS siparişinin varlığını kontrol et
    if (data.orderId) {
        const posOrderExists = await this.prisma.order.findUnique({
            where: { id: data.orderId, deletedAt: null }
        });
        if (!posOrderExists) {
            throw new NotFoundException(`POS Order with ID "${data.orderId}" not found.`);
        }
        // Ayrıca, bu POS siparişinin zaten bir onlineOrder ile ilişkili olup olmadığını kontrol et
        const existingMappingToPosOrder = await this.prisma.onlineOrder.findUnique({
            where: { orderId: data.orderId }
        });
        if (existingMappingToPosOrder) {
            throw new ConflictException(`POS Order with ID "${data.orderId}" is already linked to another online order.`);
        }
    }

    // OnlineOrder'ı oluştur
    const onlineOrder = await this.prisma.onlineOrder.create({
      data: {
        platformId: data.platformId,
        orderId: data.orderId,
        platformOrderId: data.platformOrderId,
        platformOrderNo: data.platformOrderNo,
        customerName: data.customerName,
        customerPhone: data.phone,
        customerEmail: data.customerEmail,
        deliveryAddress: data.deliveryAddress,
        deliveryNote: data.deliveryNote,
        orderData: data.orderData || {},
        status: data.status || OnlineOrderStatus.PENDING,
        platformStatus: data.platformStatus,
        subtotal: parseFloat(data.subtotal.toFixed(2)),
        deliveryFee: data.deliveryFee !== undefined ? parseFloat(data.deliveryFee.toFixed(2)) : 0,
        serviceFee: data.serviceFee !== undefined ? parseFloat(data.serviceFee.toFixed(2)) : 0,
        discount: data.discount !== undefined ? parseFloat(data.discount.toFixed(2)) : 0,
        totalAmount: parseFloat(data.totalAmount.toFixed(2)),
        commissionAmount: data.commissionAmount !== undefined ? parseFloat(data.commissionAmount.toFixed(2)) : 0,
        netAmount: data.netAmount !== undefined ? parseFloat(data.netAmount.toFixed(2)) : 0,
        paymentMethod: data.paymentMethod,
        isPaid: data.isPaid || false,
        orderedAt: data.orderedAt,
        requestedAt: data.requestedAt,
        rejectReason: data.rejectReason,
        cancelReason: data.cancelReason,
      },
    });
    
    // Eğer POS siparişi ile bağlantı varsa, order.onlineOrder ilişkisini güncelle
    if (onlineOrder.orderId) {
        await this.prisma.order.update({
            where: { id: onlineOrder.orderId },
            data: { onlineOrder: { connect: { id: onlineOrder.id } } }
        });
    }

    return onlineOrder;
  }

  async findAllOnlineOrders(
    platformId?: string,
    orderId?: string,
    status?: OnlineOrderStatus,
    platformOrderNo?: string,
    startDate?: Date,
    endDate?: Date,
  ) {
    return this.prisma.onlineOrder.findMany({
      where: {
        platformId: platformId || undefined,
        orderId: orderId || undefined,
        status: status || undefined,
        platformOrderNo: platformOrderNo || undefined,
        orderedAt: {
          gte: startDate || undefined,
          lte: endDate || undefined,
        },
      },
      include: {
        platform: { select: { id: true, name: true, code: true } },
        order: { select: { id: true, orderNumber: true, totalAmount: true, status: true } },
      },
      orderBy: { orderedAt: 'desc' },
    });
  }

  async findOneOnlineOrder(id: string) {
    const onlineOrder = await this.prisma.onlineOrder.findUnique({
      where: { id },
      include: {
        platform: { select: { id: true, name: true, code: true } },
        order: { select: { id: true, orderNumber: true, totalAmount: true, status: true } },
      },
    });
    if (!onlineOrder) {
      throw new NotFoundException(`Online order with ID "${id}" not found.`);
    }
    return onlineOrder;
  }

  async updateOnlineOrder(id: string, data: UpdateOnlineOrderDto) {
    const existingOnlineOrder = await this.findOneOnlineOrder(id);

    try {
      const updatedOnlineOrder = await this.prisma.onlineOrder.update({
        where: { id },
        data: {
            status: data.status,
            platformStatus: data.platformStatus,
            acceptedAt: data.acceptedAt,
            rejectedAt: data.rejectedAt,
            preparingAt: data.preparingAt,
            readyAt: data.readyAt,
            deliveringAt: data.deliveringAt,
            deliveredAt: data.deliveredAt,
            cancelledAt: data.cancelledAt,
            rejectReason: data.rejectReason,
            cancelReason: data.cancelReason,
            isPaid: data.isPaid,
            orderData: data.orderData,
        },
      });

      // OnlineOrderStatus değişikliği olursa, buna göre POS Order status'u güncellenebilir.
      // Örneğin, OnlineOrder ACCEPTED olduğunda POS Order da CONFIRMED olabilir.
      if (data.status && updatedOnlineOrder.orderId) {
        let posOrderStatus: OrderStatus | undefined;
        switch (data.status) {
            case OnlineOrderStatus.ACCEPTED: posOrderStatus = OrderStatus.CONFIRMED; break;
            case OnlineOrderStatus.PREPARING: posOrderStatus = OrderStatus.PREPARING; break;
            case OnlineOrderStatus.READY: posOrderStatus = OrderStatus.READY; break;
            case OnlineOrderStatus.DELIVERING: posOrderStatus = OrderStatus.DELIVERED; break; // DELIVERY tipi siparişler için
            case OnlineOrderStatus.DELIVERED: posOrderStatus = OrderStatus.COMPLETED; break;
            case OnlineOrderStatus.CANCELLED: posOrderStatus = OrderStatus.CANCELLED; break;
            case OnlineOrderStatus.REJECTED: posOrderStatus = OrderStatus.CANCELLED; break; // Reddedilenler de iptal gibi
            // Diğer durumlar için varsayılan bırakılabilir veya özel mapleme yapılabilir
        }
        if (posOrderStatus) {
            await this.prisma.order.update({
                where: { id: updatedOnlineOrder.orderId },
                data: { status: posOrderStatus }
            });
        }
      }

      return updatedOnlineOrder;
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Online order with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removeOnlineOrder(id: string) {
    // OnlineOrder modelinde soft delete alanı yok, bu nedenle fiziksel silme yapıyoruz.
    // Ancak bu tür finansal ve log kayıtları genellikle silinmez, durumları güncellenir.
    // Şimdilik fiziksel silme yapıyoruz.
    try {
      const onlineOrder = await this.prisma.onlineOrder.delete({
        where: { id },
      });

      // Eğer bağlı bir POS Order varsa, ilişkisini kopar
      if (onlineOrder.orderId) {
        await this.prisma.order.update({
            where: { id: onlineOrder.orderId },
            data: { onlineOrder: { disconnect: true } }
        });
      }

      return onlineOrder;
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Online order with ID "${id}" not found.`);
      }
      throw error;
    }
  }
}
