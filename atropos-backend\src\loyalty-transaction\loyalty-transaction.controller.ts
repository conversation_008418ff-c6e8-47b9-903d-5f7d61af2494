// src/loyalty-transaction/loyalty-transaction.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { LoyaltyTransactionService } from './loyalty-transaction.service';
import { CreateLoyaltyTransactionDto } from './dto/create-loyalty-transaction.dto';
import { UpdateLoyaltyTransactionDto } from './dto/update-loyalty-transaction.dto';
import { LoyaltyTransactionType } from '../../generated/prisma';
import { ParseOptionalDatePipe } from '../common/pipes/parse-optional-date.pipe'; // Tarih Pipe'ı
import { ParseOptionalEnumPipe } from '../common/pipes/parse-optional-enum.pipe'; // Enum Pipe'ı

@Controller('loyalty-transaction')
export class LoyaltyTransactionController {
  constructor(private readonly loyaltyTransactionService: LoyaltyTransactionService) {}

  @Post() // POST /loyalty-transaction
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createLoyaltyTransactionDto: CreateLoyaltyTransactionDto) {
    return this.loyaltyTransactionService.createLoyaltyTransaction(createLoyaltyTransactionDto);
  }

  @Get() // GET /loyalty-transaction?cardId=...&orderId=...&type=...&startDate=...&endDate=...
  findAll(
    @Query('cardId') cardId?: string,
    @Query('orderId') orderId?: string,
    @Query('type', new ParseOptionalEnumPipe(LoyaltyTransactionType)) type?: LoyaltyTransactionType,
    @Query('createdBy') createdBy?: string,
    @Query('startDate', ParseOptionalDatePipe) startDate?: Date,
    @Query('endDate', ParseOptionalDatePipe) endDate?: Date,
  ) {
    return this.loyaltyTransactionService.findAllLoyaltyTransactions(
      cardId,
      orderId,
      type,
      createdBy,
      startDate,
      endDate,
    );
  }

  @Get(':id') // GET /loyalty-transaction/:id
  findOne(@Param('id') id: string) {
    return this.loyaltyTransactionService.findOneLoyaltyTransaction(id);
  }

  @Patch(':id') // PATCH /loyalty-transaction/:id
  update(@Param('id') id: string, @Body() updateLoyaltyTransactionDto: UpdateLoyaltyTransactionDto) {
    return this.loyaltyTransactionService.updateLoyaltyTransaction(id, updateLoyaltyTransactionDto);
  }

  @Delete(':id') // DELETE /loyalty-transaction/:id (Fiziksel silme)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.loyaltyTransactionService.removeLoyaltyTransaction(id);
  }
}
