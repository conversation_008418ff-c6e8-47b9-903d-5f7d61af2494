// src/audit-log/dto/create-audit-log.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsIP,
  IsJSON,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateAuditLogDto {
  @IsString()
  @IsOptional() // nullable for system actions
  userId?: string; // Hareketi yapan kullanıcı ID'si

  @IsString()
  @IsNotEmpty()
  action: string; // "CREATE_PRODUCT", "UPDATE_ORDER", "LOGIN"

  @IsString()
  @IsOptional()
  entityType?: string; // "Product", "Order", "User"

  @IsString()
  @IsOptional()
  entityId?: string; // Etkilenen varlığın ID'si

  @IsOptional()
  details?: any; // Değişen veriler, eski/yeni de<PERSON> (JSON)

  @IsString()
  @IsOptional()
  @IsIP('4') // Sadece IPv4 için
  ipAddress?: string;

  @IsString()
  @IsOptional()
  userAgent?: string;

  // timestamp alanı servis tarafından otomatik atanacak
}
