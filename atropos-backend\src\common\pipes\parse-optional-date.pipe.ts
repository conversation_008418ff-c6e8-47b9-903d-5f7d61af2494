// src/common/pipes/parse-optional-date.pipe.ts
import { PipeTransform, Injectable, ArgumentMetadata, BadRequestException } from '@nestjs/common';

@Injectable()
export class ParseOptionalDatePipe implements PipeTransform<string, Date | undefined> {
  transform(value: string, metadata: ArgumentMetadata): Date | undefined {
    if (!value) {
      return undefined;
    }
    const date = new Date(value);
    if (isNaN(date.getTime())) {
      throw new BadRequestException(`Validation failed (ISO 8601 date string is expected for ${metadata.data}).`);
    }
    return date;
  }
}
