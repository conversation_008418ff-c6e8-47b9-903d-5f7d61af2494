// src/product/dto/create-product.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsDecimal,
  Min,
  IsBoolean,
  IsEnum,
  IsArray,
  IsInt,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ProductUnit } from '../../../generated/prisma'; // Prisma'dan ProductUnit enum'ını import et

export class CreateProductDto {
  @IsString()
  @IsNotEmpty()
  companyId: string;

  @IsString()
  @IsNotEmpty()
  categoryId: string; // Hangi kategoriye bağlı olduğu

  @IsString()
  @IsNotEmpty()
  code: string; // SKU

  @IsString()
  @IsOptional()
  barcode?: string; // EAN-13, QR, etc.

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  shortDescription?: string; // QR menü için

  @IsString()
  @IsOptional()
  image?: string;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  images?: string[]; // Çoklu resim desteği

  @Type(() => Number)
  @Min(0)
  @IsNotEmpty()
  basePrice: number;

  @IsString()
  @IsNotEmpty()
  taxId: string; // Hangi vergi oranına bağlı olduğu

  @Type(() => Number)
  @Min(0)
  @IsOptional()
  costPrice?: number;

  @Type(() => Number)
  @Min(0)
  @IsOptional()
  profitMargin?: number; // 5,2 için uygun validasyon yok, genel ondalık

  @IsBoolean()
  @IsOptional()
  trackStock?: boolean;

  @IsEnum(ProductUnit)
  @IsNotEmpty()
  unit: ProductUnit;

  @Type(() => Number)
  @Min(0)
  @IsOptional()
  criticalStock?: number;

  @IsBoolean()
  @IsOptional()
  available?: boolean;

  @IsBoolean()
  @IsOptional()
  sellable?: boolean;

  @IsInt()
  @Min(0)
  @IsOptional()
  preparationTime?: number;

  @IsInt()
  @Min(0)
  @IsOptional()
  calories?: number;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  allergens?: string[];

  @IsBoolean()
  @IsOptional()
  hasVariants?: boolean;

  @IsBoolean()
  @IsOptional()
  hasModifiers?: boolean;

  @IsBoolean()
  @IsOptional()
  showInMenu?: boolean;

  @IsBoolean()
  @IsOptional()
  featured?: boolean;

  @IsInt()
  @Min(0)
  @IsOptional()
  displayOrder?: number;

  @IsBoolean()
  @IsOptional()
  active?: boolean;
}
