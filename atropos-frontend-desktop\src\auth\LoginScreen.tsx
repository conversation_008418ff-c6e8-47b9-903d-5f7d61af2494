// atropos-frontend-desktop/src/auth/LoginScreen.tsx
import React, { useState, useEffect } from 'react'; // useEffect'i import et
import {
  Button,
  Field,
  Input,
  makeStyles,
  shorthands,
  tokens,
  Text,
  Spinner,
  Card,
  CardHeader,
  Avatar,
} from '@fluentui/react-components';
import { CheckmarkCircleFilled, DismissCircleFilled } from '@fluentui/react-icons';

// Stil tanımlamaları (önceki stilleri koru, eklemeler yap)
const useStyles = makeStyles({
  // ... (mevcut container, loginBox, avatarContainer, form, message, success, error stilleri)
  container: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: tokens.colorNeutralBackgroundAlpha,
    ...shorthands.borderRadius(tokens.borderRadiusXLarge),
    boxShadow: tokens.shadow64,
    backdropFilter: 'blur(30px) saturate(180%)',
    border: `1px solid ${tokens.colorNeutralStrokeAlpha}`,
    ...shorthands.padding('48px'),
    minWidth: '400px',
    maxWidth: '450px',
    minHeight: '450px',
    textAlign: 'center',
    boxSizing: 'border-box',
    position: 'relative', // Konumlandırma için
  },
  loginBox: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    ...shorthands.gap('24px'),
    width: '100%',
  },
  avatarContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    ...shorthands.gap('12px'),
  },
  form: {
    display: 'flex',
    flexDirection: 'column',
    ...shorthands.gap('20px'),
    width: '100%',
  },
  inputField: {
    '& .fui-Field__label': {
      color: 'rgba(0, 0, 0, 0.8)',
      fontWeight: '500',
      fontSize: '14px',
      marginBottom: '8px',
    },
    '& .fui-Input': {
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      backdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.3)',
      borderRadius: '6px',
      padding: '12px 16px',
      fontSize: '14px',
      transition: 'all 0.2s ease',
      '&:hover': {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderColor: 'rgba(255, 255, 255, 0.5)',
      },
      '&:focus': {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: tokens.colorBrandBackground,
        boxShadow: `0 0 0 2px ${tokens.colorBrandBackground}40`,
      },
    },
  },
  loginButton: {
    backgroundColor: tokens.colorBrandBackground,
    borderRadius: '6px',
    padding: '12px 24px',
    fontSize: '14px',
    fontWeight: '600',
    border: 'none',
    transition: 'all 0.2s ease',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
    '&:hover': {
      backgroundColor: tokens.colorBrandBackgroundHover,
      transform: 'translateY(-1px)',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
    },
    '&:active': {
      transform: 'translateY(0)',
    },
  },
  message: {
    width: '100%',
    ...shorthands.padding('8px'),
    ...shorthands.borderRadius('4px'),
    boxSizing: 'border-box',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  success: {
    backgroundColor: tokens.colorPaletteGreenBackground3,
    color: tokens.colorPaletteGreenForeground3,
  },
  error: {
    backgroundColor: tokens.colorPaletteRedBackground3,
    color: tokens.colorPaletteRedForeground3,
  },
  options: {
    display: 'flex',
    ...shorthands.gap('16px'),
    marginTop: '10px',
  },
  otherUser: {
    marginTop: '20px',
    ...shorthands.padding('10px 0'),
    borderTop: `1px solid ${tokens.colorNeutralStroke1}`,
    width: '100%',
    textAlign: 'center',
  },
  // Yeni eklenen stiller
  userSelectionArea: {
    position: 'fixed', // Fixed konumlandırma - sayfanın sol alt köşesi
    bottom: '20px', // Alt kenardan boşluk
    left: '20px', // Sol kenardan boşluk
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    ...shorthands.gap('8px'),
    zIndex: 1000, // En üstte olması için
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    ...shorthands.padding('12px'),
    ...shorthands.borderRadius('8px'),
    backdropFilter: 'blur(10px)',
    border: '1px solid rgba(255, 255, 255, 0.3)',
    boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)',
  },
  userOption: {
    display: 'flex',
    alignItems: 'center',
    ...shorthands.gap('8px'),
    cursor: 'pointer',
    ...shorthands.padding('4px 8px'),
    ...shorthands.borderRadius(tokens.borderRadiusMedium),
    '&:hover': {
      backgroundColor: tokens.colorNeutralBackground3,
    },
  },
});

// Kullanıcı arayüzü için basit User modeli
interface User {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  avatar?: string;
}

interface LoginScreenProps {
  onLoginSuccess: (token: string) => void;
}

const LoginScreen: React.FC<LoginScreenProps> = ({ onLoginSuccess }) => {
  const styles = useStyles();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const [allUsers, setAllUsers] = useState<User[]>([]); // Tüm kullanıcılar için state
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null); // Seçilen kullanıcı ID'si
  const [usersLoading, setUsersLoading] = useState(true); // Kullanıcıları yükleme durumu
  const [usersError, setUsersError] = useState<string | null>(null); // Kullanıcı yükleme hatası

  const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';

  // Tüm kullanıcıları çekmek için useEffect
  useEffect(() => {
    const fetchUsers = async () => {
      setUsersLoading(true);
      setUsersError(null);
      try {
        // Backend'den tüm kullanıcıları çek (profil endpoint'i ile kimlik doğrulama olmadan)
        // Normalde bu endpoint'in korunması gerekir, ancak Login ekranında kullanmak için
        // geçici olarak User modülündeki findAllUsers'ı kullanabiliriz (şimdilik)
        // Veya backend'de kimlik doğrulama gerektirmeyen özel bir 'public' kullanıcı listesi endpoint'i açılabilir.
        // Şimdilik /user endpoint'ini kullanıyoruz, AuthGuard uygulanmadığını varsayarak.
        const response = await fetch(`${API_URL}/user`);
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || `Kullanıcılar yüklenirken hata oluştu! status: ${response.status}`);
        }
        const data: User[] = await response.json();
        setAllUsers(data);
        if (data.length > 0) {
          // İlk kullanıcıyı varsayılan olarak seç
          setSelectedUserId(data[0].id);
          setUsername(data[0].username);
        }
      } catch (err: any) {
        setUsersError(err.message);
        console.error("Error fetching users:", err);
      } finally {
        setUsersLoading(false);
      }
    };
    fetchUsers();
  }, []);

  // Seçilen kullanıcı değiştiğinde username'i güncelle
  useEffect(() => {
    if (selectedUserId) {
      const selectedUser = allUsers.find(user => user.id === selectedUserId);
      if (selectedUser) {
        setUsername(selectedUser.username);
        // Avatarı da burada güncelle
      }
    } else {
        setUsername('');
    }
  }, [selectedUserId, allUsers]);


  const handleLogin = async (event: React.FormEvent) => {
    event.preventDefault();
    setLoading(true);
    setError(null);
    setSuccessMessage(null);

    // Şifrenin doğru girildiğinden emin ol
    if (!username || !password) {
        setError('Kullanıcı adı ve şifre boş bırakılamaz.');
        setLoading(false);
        return;
    }

    try {
      const response = await fetch(`${API_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Giriş başarısız oldu.');
      }

      const data = await response.json();
      setSuccessMessage('Giriş başarılı! Yönlendiriliyorsunuz...');
      // Token'ı bir sonraki adımda güvenli bir şekilde saklayacağız
      onLoginSuccess(data.access_token);
    } catch (err: any) {
      setError(err.message);
      console.error('Login error:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div className={styles.container}>
        <div className={styles.loginBox}>
          <div className={styles.avatarContainer}>
            <Avatar
              name={username || "Kullanıcı"}
              size={96}
              // Kullanıcının avatar URL'si varsa onu kullanabiliriz
              // image={{ src: selectedUser?.avatar || "default-avatar.png" }}
            />
            <Text as="h2" size={600} weight="semibold">{username || "Kullanıcı Adı"}</Text>
            <Text size={200}>Lütfen şifrenizi girin.</Text>
          </div>

        <form onSubmit={handleLogin} className={styles.form}>
          <Field label="Kullanıcı Adı" required>
            <Input
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="Kullanıcı adınız"
              disabled={loading || usersLoading}
              appearance="outline"
              readOnly // Sadece sol alttaki listeden seçim yapılabilir
            />
          </Field>
          <Field label="Şifre" required>
            <Input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Şifreniz"
              disabled={loading || usersLoading}
              appearance="outline"
            />
          </Field>

          {error && (
            <Card className={`${styles.message} ${styles.error}`} appearance="outline">
              <CardHeader
                header={<Text weight="semibold">Hata:</Text>}
                description={<Text size={200}>{error}</Text>}
                action={<DismissCircleFilled primaryFill={tokens.colorPaletteRedForeground3} />}
              />
            </Card>
          )}
          {successMessage && (
            <Card className={`${styles.message} ${styles.success}`} appearance="outline">
              <CardHeader
                header={<Text weight="semibold">Başarılı:</Text>}
                description={<Text size={200}>{successMessage}</Text>}
                action={<CheckmarkCircleFilled primaryFill={tokens.colorPaletteGreenForeground3} />}
              />
            </Card>
          )}

          <Button type="submit" appearance="primary" disabled={loading || usersLoading}>
            {loading ? <Spinner size="tiny" labelPosition="after" label="Giriş Yapılıyor..." /> : 'Giriş Yap'}
          </Button>
        </form>

        {/* Windows 11 Login ekranındaki diğer seçenekler benzeri butonlar */}
        <div className={styles.options}>
          <Button appearance="subtle">Giriş Seçenekleri</Button>
          <Button appearance="subtle">PIN ile Giriş</Button>
        </div>

        {/* Bu kısım, sol altta kullanıcı seçimini göstermek için daha dinamik olabilir */}
        {/* Şimdilik genel bir "Başka bir kullanıcı" Link'i olarak kalıyor */}
        {/* Daha fazla kullanıcı olduğunda bu alanı değiştirebiliriz */}
        <div className={styles.otherUser}>
          {allUsers.length > 1 && ( // Birden fazla kullanıcı varsa "Başka bir kullanıcı" göstermenin anlamı olur
              <Button appearance="subtle" onClick={() => {
                  setSelectedUserId(null); // Seçimi temizle
                  setUsername('');
                  setPassword('');
              }}>
                  Başka bir kullanıcı
              </Button>
          )}
        </div>
      </div>

      {/* Sol alttaki kullanıcı seçim alanı - Sayfanın sol alt köşesinde */}
      {/* Windows 11'deki gibi sol altta kullanıcı listesi */}
      {usersLoading ? (
        <div className={styles.userSelectionArea}>
          <Spinner size="tiny" labelPosition="after" label="Kullanıcılar yükleniyor..." />
        </div>
      ) : usersError ? (
        <div className={styles.userSelectionArea}>
          <Text style={{ color: 'red' }}>{usersError}</Text>
        </div>
      ) : (
        <div className={styles.userSelectionArea}>
          <Text size={200} weight="semibold" style={{ marginBottom: '8px', color: 'rgba(0,0,0,0.7)' }}>
            Kullanıcı Seç:
          </Text>
          {allUsers.map(user => (
            <div
              key={user.id}
              className={styles.userOption}
              onClick={() => {
                setSelectedUserId(user.id);
                // Şifre alanını temizle
                setPassword('');
              }}
              style={{
                opacity: selectedUserId === user.id ? 1 : 0.7,
                backgroundColor: selectedUserId === user.id ? 'rgba(0, 120, 212, 0.1)' : 'transparent'
              }}
            >
              <Avatar name={user.firstName + ' ' + user.lastName} size={32} />
              <Text weight="semibold">{user.firstName} {user.lastName}</Text>
              {selectedUserId === user.id && <CheckmarkCircleFilled primaryFill={tokens.colorBrandBackground} />}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default LoginScreen;
