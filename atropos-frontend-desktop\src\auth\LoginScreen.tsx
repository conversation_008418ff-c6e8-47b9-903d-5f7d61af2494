// src/auth/LoginScreen.tsx
import React, { useState } from 'react';
import {
  Button,
  Field,
  Input,
  makeStyles,
  shorthands,
  tokens,
  Text,
  Spinner,
  Card,
  CardHeader,
  Avatar,
} from '@fluentui/react-components';
import { CheckmarkCircleFilled, DismissCircleFilled } from '@fluentui/react-icons'; // İkonlar için

// Stil tanımlamaları (Windows 11 login ekranından esinlenerek)
const useStyles = makeStyles({
  container: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    ...shorthands.padding('40px'),
    // Windows 11 Acrylic Material efekti
    backgroundColor: 'rgba(243, 243, 243, 0.7)', // Daha doğal Windows 11 rengi
    ...shorthands.borderRadius('8px'), // Windows 11 standart border radius
    boxShadow: `
      0 8px 32px 0 rgba(31, 38, 135, 0.37),
      inset 0 1px 0 0 rgba(255, 255, 255, 0.5),
      0 1px 0 0 rgba(255, 255, 255, 0.25)
    `, // Çok katmanlı gölge efekti
    backdropFilter: 'blur(16px) saturate(180%)', // Daha güçlü blur ve saturasyon
    WebkitBackdropFilter: 'blur(16px) saturate(180%)',
    border: '1px solid rgba(255, 255, 255, 0.18)', // Daha ince border
    borderTop: '1px solid rgba(255, 255, 255, 0.5)', // Üst kenarda daha belirgin highlight
    width: '400px',
    maxWidth: '90vw',
    position: 'relative',
    // Ek glassmorphism detayları
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 50%)',
      ...shorthands.borderRadius('8px'),
      pointerEvents: 'none',
      zIndex: 1,
    },
    // İçerik z-index'ini artır
    '& > *': {
      position: 'relative',
      zIndex: 2,
    },
    // Hover efekti
    '&:hover': {
      backgroundColor: 'rgba(243, 243, 243, 0.8)',
      transform: 'translateY(-2px)',
      boxShadow: `
        0 12px 40px 0 rgba(31, 38, 135, 0.4),
        inset 0 1px 0 0 rgba(255, 255, 255, 0.6),
        0 1px 0 0 rgba(255, 255, 255, 0.3)
      `,
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    },
    // Transition
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  },
  loginBox: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    ...shorthands.gap('20px'),
    width: '100%',
  },
  avatarContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    ...shorthands.gap('8px'),
    marginBottom: '10px',
  },
  form: {
    display: 'flex',
    flexDirection: 'column',
    ...shorthands.gap('20px'),
    width: '100%',
  },
  inputField: {
    '& .fui-Field__label': {
      color: 'rgba(0, 0, 0, 0.8)',
      fontWeight: '500',
      fontSize: '14px',
      marginBottom: '8px',
    },
    '& .fui-Input': {
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      backdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.3)',
      borderRadius: '6px',
      padding: '12px 16px',
      fontSize: '14px',
      transition: 'all 0.2s ease',
      '&:hover': {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderColor: 'rgba(255, 255, 255, 0.5)',
      },
      '&:focus': {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: tokens.colorBrandBackground,
        boxShadow: `0 0 0 2px ${tokens.colorBrandBackground}40`,
      },
    },
  },
  loginButton: {
    backgroundColor: tokens.colorBrandBackground,
    borderRadius: '6px',
    padding: '12px 24px',
    fontSize: '14px',
    fontWeight: '600',
    border: 'none',
    transition: 'all 0.2s ease',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
    '&:hover': {
      backgroundColor: tokens.colorBrandBackgroundHover,
      transform: 'translateY(-1px)',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
    },
    '&:active': {
      transform: 'translateY(0)',
    },
  },
  message: {
    textAlign: 'center',
    ...shorthands.padding('12px 16px'),
    ...shorthands.borderRadius('6px'),
    backdropFilter: 'blur(10px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
  },
  success: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderColor: 'rgba(76, 175, 80, 0.3)',
    color: '#2e7d32',
  },
  error: {
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
    borderColor: 'rgba(244, 67, 54, 0.3)',
    color: '#c62828',
  },
  // Windows 11 login ekranındaki 'Sign-in options' ve 'Other user' benzeri alanlar için
  options: {
    display: 'flex',
    ...shorthands.gap('12px'),
    marginTop: '10px',
  },
  otherUser: {
    marginTop: '20px',
    ...shorthands.padding('10px 0'),
    borderTop: `1px solid ${tokens.colorNeutralStroke1}`,
    width: '100%',
    textAlign: 'center',
  },
});

interface LoginScreenProps {
  onLoginSuccess: (token: string) => void;
}

const LoginScreen: React.FC<LoginScreenProps> = ({ onLoginSuccess }) => {
  const styles = useStyles();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';

  const handleLogin = async (event: React.FormEvent) => {
    event.preventDefault(); // Formun varsayılan gönderimini engelle
    setLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const response = await fetch(`${API_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Giriş başarısız oldu.');
      }

      const data = await response.json();
      setSuccessMessage('Giriş başarılı! Yönlendiriliyorsunuz...');
      // Token'ı bir sonraki adımda güvenli bir şekilde saklayacağız
      onLoginSuccess(data.access_token);
    } catch (err: any) {
      setError(err.message);
      console.error('Login error:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.loginBox}>
        <div className={styles.avatarContainer}>
          <Avatar name={username || "Kullanıcı"} size={72} /> {/* Varsayılan avatar */}
          <Text as="h2" size={600}>{username || "Kullanıcı Adı"}</Text>
          <Text size={200}>Lütfen şifrenizi girin.</Text>
        </div>

        <form onSubmit={handleLogin} className={styles.form}>
          <Field label="Kullanıcı Adı" required className={styles.inputField}>
            <Input
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="Kullanıcı adınız"
              disabled={loading}
              appearance="outline"
            />
          </Field>
          <Field label="Şifre" required className={styles.inputField}>
            <Input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Şifreniz"
              disabled={loading}
              appearance="outline"
            />
          </Field>

          {error && (
            <Card className={`${styles.message} ${styles.error}`} appearance="outline">
              <CardHeader
                header={<Text weight="semibold">Hata:</Text>}
                description={<Text size={200}>{error}</Text>}
                action={<DismissCircleFilled primaryFill={tokens.colorPaletteRedForeground3} />}
              />
            </Card>
          )}
          {successMessage && (
            <Card className={`${styles.message} ${styles.success}`} appearance="outline">
              <CardHeader
                header={<Text weight="semibold">Başarılı:</Text>}
                description={<Text size={200}>{successMessage}</Text>}
                action={<CheckmarkCircleFilled primaryFill={tokens.colorPaletteGreenForeground3} />}
              />
            </Card>
          )}

          <Button
            type="submit"
            appearance="primary"
            disabled={loading}
            className={styles.loginButton}
          >
            {loading ? <Spinner size="tiny" labelPosition="after" label="Giriş Yapılıyor..." /> : 'Giriş Yap'}
          </Button>
        </form>

        {/* Windows 11 Login ekranındaki diğer seçenekler benzeri butonlar */}
        <div className={styles.options}>
          <Button appearance="subtle" size="small">Giriş Seçenekleri</Button>
        </div>
      </div>
    </div>
  );
};

export default LoginScreen;
