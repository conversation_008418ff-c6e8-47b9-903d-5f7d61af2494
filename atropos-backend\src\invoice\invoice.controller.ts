// src/invoice/invoice.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { InvoiceService } from './invoice.service';
import { CreateInvoiceDto } from './dto/create-invoice.dto';
import { UpdateInvoiceDto } from './dto/update-invoice.dto';
// Enum'ı object olarak tanımlayalım
const InvoiceType = {
  RECEIPT: 'RECEIPT',
  INVOICE: 'INVOICE',
  E_ARCHIVE: 'E_ARCHIVE',
  E_INVOICE: 'E_INVOICE',
  PROFORMA: 'PROFORMA',
  RETURN: 'RETURN'
} as const;

type InvoiceType = typeof InvoiceType[keyof typeof InvoiceType];
import { ParseOptionalDatePipe } from '../common/pipes/parse-optional-date.pipe'; // Kendi pipe'ımızı import et
import { ParseOptionalEnumPipe } from '../common/pipes/parse-optional-enum.pipe';

@Controller('invoice')
export class InvoiceController {
  constructor(private readonly invoiceService: InvoiceService) {}

  @Post() // POST /invoice
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createInvoiceDto: CreateInvoiceDto) {
    return this.invoiceService.createInvoice(createInvoiceDto);
  }

  @Get() // GET /invoice?orderId=...&customerTaxNo=...&invoiceType=...&startDate=...&endDate=...
  findAll(
    @Query('orderId') orderId?: string,
    @Query('customerTaxNo') customerTaxNo?: string,
    @Query('invoiceType', new ParseOptionalEnumPipe(InvoiceType)) invoiceType?: InvoiceType,
    @Query('startDate', ParseOptionalDatePipe) startDate?: Date,
    @Query('endDate', ParseOptionalDatePipe) endDate?: Date,
  ) {
    return this.invoiceService.findAllInvoices(
      orderId,
      customerTaxNo,
      invoiceType,
      startDate,
      endDate,
    );
  }

  @Get(':id') // GET /invoice/:id
  findOne(@Param('id') id: string) {
    return this.invoiceService.findOneInvoice(id);
  }

  @Patch(':id') // PATCH /invoice/:id
  update(@Param('id') id: string, @Body() updateInvoiceDto: UpdateInvoiceDto) {
    return this.invoiceService.updateInvoice(id, updateInvoiceDto);
  }

  @Delete(':id') // DELETE /invoice/:id (Soft delete)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.invoiceService.removeInvoice(id);
  }
}
