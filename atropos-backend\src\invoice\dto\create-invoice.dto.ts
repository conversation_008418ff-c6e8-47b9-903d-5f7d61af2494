// src/invoice/dto/create-invoice.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsDecimal,
  Min,
  IsBoolean,
  IsEnum,
  IsJSON,
  IsNumber,
  IsEmail,
} from 'class-validator';
import { Type } from 'class-transformer';
// Enum'ları object olarak tanımlayalım
const InvoiceType = {
  RECEIPT: 'RECEIPT',
  INVOICE: 'INVOICE',
  E_ARCHIVE: 'E_ARCHIVE',
  E_INVOICE: 'E_INVOICE',
  PROFORMA: 'PROFORMA',
  RETURN: 'RETURN'
} as const;

const EArchiveStatus = {
  PENDING: 'PENDING',
  SENT: 'SENT',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  CANCELLED: 'CANCELLED'
} as const;

type InvoiceType = typeof InvoiceType[keyof typeof InvoiceType];
type EArchiveStatus = typeof EArchiveStatus[keyof typeof EArchiveStatus];

export class CreateInvoiceDto {
  @IsString()
  @IsOptional() // orderId nullable, çünkü manuel fatura da kesilebilir
  orderId?: string;

  @IsEnum(InvoiceType)
  @IsNotEmpty()
  invoiceType: InvoiceType;

  @IsString()
  @IsNotEmpty()
  serialNo: string; // "A", "B" gibi seri

  @IsOptional()
  sequenceNo?: string; // "2024000001" gibi sıra numarası - otomatik oluşturulur

  @IsString()
  @IsOptional()
  customerName?: string;

  @IsString()
  @IsOptional()
  taxNumber?: string; // Müşterinin vergi numarası

  @IsString()
  @IsOptional()
  taxOffice?: string;

  @IsString()
  @IsOptional()
  address?: string;

  @IsString()
  @IsOptional()
  phone?: string;

  @IsString()
  @IsOptional()
  @IsEmail()
  email?: string;

  // Tutarlar servis tarafından hesaplanacak, ancak manuel faturalarda gönderilebilir
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  subtotal?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  discountAmount?: number;

  @IsOptional()
  taxDetails?: any; // KDV matrahları (JSON)

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  taxAmount?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  totalAmount?: number;

  @IsString()
  @IsOptional()
  totalAmountText?: string; // "Bin iki yüz elli TL"

  @IsString()
  @IsOptional()
  uuid?: string; // e-Arşiv UUID

  @IsEnum(EArchiveStatus)
  @IsOptional()
  eArchiveStatus?: EArchiveStatus;

  @IsOptional()
  eArchiveResponse?: any; // JSON

  @IsBoolean()
  @IsOptional()
  isCancelled?: boolean;

  @IsString()
  @IsOptional()
  cancelReason?: string;

  @IsString()
  @IsOptional()
  cancelledInvoiceId?: string; // İptal edilen faturanın ID'si

  @IsString()
  @IsOptional()
  pdfUrl?: string;
}
