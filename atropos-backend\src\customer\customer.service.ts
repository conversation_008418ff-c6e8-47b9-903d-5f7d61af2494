// src/customer/customer.service.ts
import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';

@Injectable()
export class CustomerService {
  constructor(private prisma: PrismaService) {}

  async createCustomer(data: CreateCustomerDto) {
    // Telefon numarası benzersiz mi kontrol et
    const existingCustomer = await this.prisma.customer.findUnique({
      where: { phone: data.phone },
    });
    if (existingCustomer) {
      throw new ConflictException(`Customer with phone number "${data.phone}" already exists.`);
    }

    return this.prisma.customer.create({ data });
  }

  async findAllCustomers(phone?: string, email?: string, taxNumber?: string) {
    return this.prisma.customer.findMany({
      where: {
        phone: phone || undefined,
        email: email || undefined,
        taxNumber: taxNumber || undefined,
        deletedAt: null,
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOneCustomer(id: string) {
    const customer = await this.prisma.customer.findUnique({
      where: { id, deletedAt: null },
      // İhtiyaç olursa ilgili ilişkileri include edebilirsin (orders, loyaltyCard, reservations, addresses, transactions)
      // include: { addresses: true, loyaltyCard: true }
    });
    if (!customer) {
      throw new NotFoundException(`Customer with ID "${id}" not found.`);
    }
    return customer;
  }

  async updateCustomer(id: string, data: UpdateCustomerDto) {
    // Telefon numarası güncelleniyorsa unique kontrolü
    if ((data as any).phone) {
        const existingCustomer = await this.prisma.customer.findUnique({
            where: { phone: (data as any).phone },
        });
        if (existingCustomer && existingCustomer.id !== id) {
            throw new ConflictException(`Phone number "${(data as any).phone}" is already in use by another customer.`);
        }
    }

    try {
      return await this.prisma.customer.update({
        where: { id, deletedAt: null },
        data,
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Customer with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removeCustomer(id: string) {
    // Bu müşteriye bağlı aktif sipariş veya rezervasyon var mı kontrol et
    const activeOrdersCount = await this.prisma.order.count({
        where: { customerId: id, status: { notIn: ['COMPLETED', 'CANCELLED', 'RETURNED'] }, deletedAt: null }
    });
    if (activeOrdersCount > 0) {
        throw new ConflictException(`Customer with ID "${id}" cannot be deleted because they have ${activeOrdersCount} active orders.`);
    }

    const activeReservationsCount = await this.prisma.reservation.count({
        where: { customerId: id, status: { notIn: ['COMPLETED', 'CANCELLED', 'NO_SHOW'] } }
    });
    if (activeReservationsCount > 0) {
        throw new ConflictException(`Customer with ID "${id}" cannot be deleted because they have ${activeReservationsCount} active reservations.`);
    }

    // Soft delete uygulaması
    try {
      return await this.prisma.customer.update({
        where: { id, deletedAt: null },
        data: { deletedAt: new Date(), blacklisted: true, marketingConsent: false, smsConsent: false, emailConsent: false }, // Soft delete ve ilgili izinleri kapat
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Customer with ID "${id}" not found or already deleted.`);
      }
      throw error;
    }
  }
}
