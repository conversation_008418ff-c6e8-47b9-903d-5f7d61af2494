// src/branch/branch.module.ts
import { Module } from '@nestjs/common';
import { BranchService } from './branch.service';
import { BranchController } from './branch.controller';
import { PrismaModule } from '../prisma/prisma.module'; // PrismaService'i kullanmak için

@Module({
  imports: [PrismaModule], // BranchService Prisma'ya bağımlı olduğu için
  controllers: [BranchController],
  providers: [BranchService],
  exports: [BranchService], // Diğer modüllerde BranchService kullanmak istersen
})
export class BranchModule {}
