// src/customer/customer.module.ts
import { Module } from '@nestjs/common';
import { CustomerService } from './customer.service';
import { CustomerController } from './customer.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [CustomerController],
  providers: [CustomerService],
  exports: [CustomerService], // Sipariş modülü CustomerService'e bağımlı olacağı için dışa aktarıyoruz
})
export class CustomerModule {}
