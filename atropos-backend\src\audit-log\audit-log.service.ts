// src/audit-log/audit-log.service.ts
import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateAuditLogDto } from './dto/create-audit-log.dto';
import { UpdateAuditLogDto } from './dto/update-audit-log.dto';

@Injectable()
export class AuditLogService {
  constructor(private prisma: PrismaService) {}

  async createAuditLog(data: CreateAuditLogDto) {
    // userId belirtilmişse kullanıcının varlığını kontrol et
    if (data.userId) {
      const userExists = await this.prisma.user.findUnique({
        where: { id: data.userId, deletedAt: null },
      });
      if (!userExists) {
        throw new NotFoundException(`User with ID "${data.userId}" not found for audit log.`);
      }
    }

    return this.prisma.auditLog.create({
      data: {
        ...data,
        timestamp: new Date(), // Otomatik zaman damgası
      },
    });
  }

  async findAllAuditLogs(userId?: string, action?: string, entityType?: string, entityId?: string, startDate?: Date, endDate?: Date) {
    return this.prisma.auditLog.findMany({
      where: {
        userId: userId || undefined,
        action: action || undefined,
        entityType: entityType || undefined,
        entityId: entityId || undefined,
        timestamp: {
          gte: startDate || undefined,
          lte: endDate ? new Date(endDate.getTime() + (24 * 60 * 60 * 1000) - 1) : undefined, // Gün sonunu dahil et
        },
      },
      include: {
        user: { select: { id: true, username: true, firstName: true, lastName: true } },
      },
      orderBy: { timestamp: 'desc' },
    });
  }

  async findOneAuditLog(id: string) {
    const log = await this.prisma.auditLog.findUnique({
      where: { id },
      include: {
        user: { select: { id: true, username: true, firstName: true, lastName: true } },
      },
    });
    if (!log) {
      throw new NotFoundException(`Audit log with ID "${id}" not found.`);
    }
    return log;
  }

  async updateAuditLog(id: string, data: UpdateAuditLogDto) {
    // Audit logları değiştirilemez olmalıdır.
    // Bu metod çağrıldığında hata fırlatmalıyız.
    throw new ForbiddenException('Audit logs cannot be updated.');
  }

  async removeAuditLog(id: string) {
    // Audit logları fiziksel olarak silinmemelidir, denetim izi bozulur.
    // Bu metod çağrıldığında hata fırlatmalıyız.
    throw new ForbiddenException('Audit logs cannot be deleted.');
    /*
    try {
      return await this.prisma.auditLog.delete({
        where: { id },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Audit log with ID "${id}" not found.`);
      }
      throw error;
    }
    */
  }
}
