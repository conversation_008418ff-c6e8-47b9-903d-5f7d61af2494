// src/daily-report/daily-report.service.ts
import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateDailyReportDto } from './dto/create-daily-report.dto';
import { UpdateDailyReportDto } from './dto/update-daily-report.dto';
import { PaymentMethodType, OrderStatus, PaymentStatus, CashMovementType } from '../../generated/prisma';

@Injectable()
export class DailyReportService {
  constructor(private prisma: PrismaService) {}

  // Raporu dinamik olarak hesaplayan yardımcı fonksiyon
  async generateReportData(branchId: string, reportDate: Date) {
    const startOfDay = new Date(reportDate);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(reportDate);
    endOfDay.setHours(23, 59, 59, 999);

    // Şube var mı kontrol et
    const branchExists = await this.prisma.branch.findUnique({
        where: { id: branchId, deletedAt: null }
    });
    if (!branchExists) {
        throw new NotFoundException(`Branch with ID "${branchId}" not found.`);
    }

    // Sipariş Verileri
    const orders = await this.prisma.order.findMany({
      where: {
        branchId,
        orderedAt: { gte: startOfDay, lte: endOfDay },
        deletedAt: null,
      },
      include: {
        items: {
          include: { product: { include: { tax: true, category: true } } },
        },
        payments: {
            where: { deletedAt: null, status: { in: ['PAID', 'PARTIALLY_PAID', 'PARTIALLY_REFUNDED', 'REFUNDED'] } }, // Sadece geçerli ödemeler
            include: { paymentMethod: true }
        }
      },
    });

    // Kasa Hareketleri (Daha detaylı çekilmeli, sadece ödemeler değil)
    const cashMovements = await this.prisma.cashMovement.findMany({
        where: {
            branchId,
            createdAt: { gte: startOfDay, lte: endOfDay }
        }
    });

    // Başlangıç Değerleri
    let totalOrders = orders.length;
    let totalItems = 0;
    let totalCustomers = 0; // Distinct customerId count
    let grossSales = 0;
    let totalDiscount = 0;
    let totalServiceCharge = 0;
    let netSales = 0;
    let totalTax = 0;
    let totalSales = 0; // Net Sales + Total Tax
    let cashSales = 0;
    let creditCardSales = 0;
    let debitCardSales = 0;
    let mealCardSales = 0;
    let otherSales = 0;
    let totalReturns = 0; // OrderStatus 'RETURNED' veya Payment.refundAmount
    let totalCancellations = 0; // OrderStatus 'CANCELLED'

    const customerIds = new Set<string>();
    const taxBreakdown: { [taxId: string]: { name: string, rate: number, base: number, amount: number } } = {};
    const categoryBreakdown: { [categoryId: string]: { name: string, grossSales: number } } = {};
    const hourlyBreakdown: { [hour: string]: number } = {};

    // Sipariş ve Ödeme Verilerini İşle
    for (const order of orders) {
      totalItems += order.items.reduce((sum, item) => sum + item.quantity.toNumber(), 0);

      if (order.customerId) {
        customerIds.add(order.customerId);
      }

      if (order.status === 'CANCELLED') {
          totalCancellations += order.totalAmount.toNumber();
          continue; // İptal edilen siparişleri diğer hesaplamalara dahil etme
      }
      if (order.status === 'RETURNED') {
          totalReturns += order.totalAmount.toNumber(); // Tüm iade edilen siparişin tutarı
          continue;
      }

      grossSales += order.subtotal.toNumber();
      totalDiscount += order.discountAmount.toNumber();
      totalServiceCharge += order.serviceCharge.toNumber();
      totalSales += order.totalAmount.toNumber(); // Net + Tax (totalAmount)
      totalTax += order.taxAmount.toNumber(); // Sadece siparişin toplam vergisi

      // Payment Method bazında satışlar
      for (const payment of order.payments) {
        // Sadece başarılı ödemeleri dikkate al
        if (['PAID', 'PARTIALLY_PAID'].includes(payment.status)) {
            switch (payment.paymentMethod.type) {
                case 'CASH':
                    cashSales += payment.amount.toNumber();
                    break;
                case 'CREDIT_CARD':
                    creditCardSales += payment.amount.toNumber();
                    break;
                case 'DEBIT_CARD':
                    debitCardSales += payment.amount.toNumber();
                    break;
                case 'MEAL_CARD':
                    mealCardSales += payment.amount.toNumber();
                    break;
                default:
                    otherSales += payment.amount.toNumber();
                    break;
            }
        } else if (['PARTIALLY_REFUNDED', 'REFUNDED'].includes(payment.status)) {
            // İade edilen miktarı düş
            totalReturns += payment.refundAmount?.toNumber() || 0;
        }
      }

      // KDV Matrahları
      for (const item of order.items) {
        const itemSubtotal = (item.unitPrice.toNumber() * item.quantity.toNumber()) - (item.discountAmount.toNumber() || (item.discountRate.toNumber() ? (item.unitPrice.toNumber() * item.discountRate.toNumber()) / 100 : 0));
        const taxRate = item.taxRate.toNumber();
        const taxAmount = (itemSubtotal * taxRate) / 100;
        const taxCode = item.product.tax.code;

        if (!taxBreakdown[taxCode]) {
          taxBreakdown[taxCode] = { name: item.product.tax.name, rate: taxRate, base: 0, amount: 0 };
        }
        taxBreakdown[taxCode].base += itemSubtotal;
        taxBreakdown[taxCode].amount += taxAmount;
      }

      // Kategori Bazlı Satışlar
      for (const item of order.items) {
        const categoryId = item.product.categoryId;
        const categoryName = item.product.category.name;
        const itemSubtotal = (item.unitPrice.toNumber() * item.quantity.toNumber()) - (item.discountAmount.toNumber() || (item.discountRate.toNumber() ? (item.unitPrice.toNumber() * item.discountRate.toNumber()) / 100 : 0));

        if (!categoryBreakdown[categoryId]) {
          categoryBreakdown[categoryId] = { name: categoryName, grossSales: 0 };
        }
        categoryBreakdown[categoryId].grossSales += itemSubtotal;
      }

      // Saatlik Satışlar
      const orderHour = order.orderedAt.getHours();
      const hourKey = `${String(orderHour).padStart(2, '0')}:00`;
      if (!hourlyBreakdown[hourKey]) {
        hourlyBreakdown[hourKey] = 0;
      }
      hourlyBreakdown[hourKey] += order.totalAmount.toNumber();
    }
    
    netSales = grossSales - totalDiscount + totalServiceCharge; // Net satış (vergisiz)
    let averageTicket = totalOrders > 0 ? totalSales / totalOrders : 0;
    totalCustomers = customerIds.size; // Tekil müşteri sayısı

    // Kasa Hareketleri Hesaplamaları
    let openingBalance = 0; // Bu, DailyReport'un kendi openingBalance'ı olacak
    let totalCashIn = 0;
    let totalCashOut = 0;

    for (const movement of cashMovements) {
        if (movement.type === CashMovementType.OPENING) {
            openingBalance = movement.amount.toNumber();
        } else if (movement.type === CashMovementType.SALE || movement.type === CashMovementType.INCOME || movement.type === CashMovementType.DEPOSIT || movement.type === CashMovementType.TRANSFER_IN || movement.type === CashMovementType.SURPLUS) {
            totalCashIn += movement.amount.toNumber();
        } else if (movement.type === CashMovementType.REFUND || movement.type === CashMovementType.EXPENSE || movement.type === CashMovementType.WITHDRAWAL || movement.type === CashMovementType.TRANSFER_OUT || movement.type === CashMovementType.SHORTAGE) {
            totalCashOut += Math.abs(movement.amount.toNumber()); // Giderleri pozitif olarak al
        }
    }
    
    // expectedBalance, actualBalance, difference genellikle kapanışta belirlenir.
    // Burada basit bir hesaplama yapabiliriz:
    const expectedBalance = openingBalance + totalCashIn - totalCashOut;
    let actualBalance = null; // Genellikle manuel girilir veya DailyReport'un update ile güncellenir
    let difference = null;

    return {
      reportDate: startOfDay, // Sadece tarih kısmı
      reportNo: await this.generateReportNumber(branchId, reportDate), // Otomatik rapor numarası
      totalOrders,
      totalItems,
      totalCustomers,
      averageTicket: parseFloat(averageTicket.toFixed(2)),
      grossSales: parseFloat(grossSales.toFixed(2)),
      totalDiscount: parseFloat(totalDiscount.toFixed(2)),
      totalServiceCharge: parseFloat(totalServiceCharge.toFixed(2)),
      netSales: parseFloat(netSales.toFixed(2)),
      totalTax: parseFloat(totalTax.toFixed(2)),
      totalSales: parseFloat(totalSales.toFixed(2)),
      cashSales: parseFloat(cashSales.toFixed(2)),
      creditCardSales: parseFloat(creditCardSales.toFixed(2)),
      debitCardSales: parseFloat(debitCardSales.toFixed(2)),
      mealCardSales: parseFloat(mealCardSales.toFixed(2)),
      otherSales: parseFloat(otherSales.toFixed(2)),
      totalReturns: parseFloat(totalReturns.toFixed(2)),
      totalCancellations: parseFloat(totalCancellations.toFixed(2)),
      openingBalance: parseFloat(openingBalance.toFixed(2)),
      totalCashIn: parseFloat(totalCashIn.toFixed(2)),
      totalCashOut: parseFloat(totalCashOut.toFixed(2)),
      expectedBalance: parseFloat(expectedBalance.toFixed(2)),
      actualBalance: actualBalance, // Manuel giriş için
      difference: difference, // Manuel hesaplanacak
      taxBreakdown: taxBreakdown, // JSON
      categoryBreakdown: categoryBreakdown, // JSON
      hourlyBreakdown: hourlyBreakdown, // JSON
    };
  }

  private async generateReportNumber(branchId: string, reportDate: Date): Promise<string> {
    const currentYear = reportDate.getFullYear();
    const latestReport = await this.prisma.dailyReport.findFirst({
        where: { branchId, reportDate: { gte: new Date(`${currentYear}-01-01T00:00:00Z`) } },
        orderBy: { createdAt: 'desc' }
    });
    let sequence = 1;
    if (latestReport && latestReport.reportNo) {
        const lastSeq = parseInt(latestReport.reportNo.match(/\d+$/)?.[0] || '0');
        sequence = lastSeq + 1;
    }
    return `Z${String(sequence).padStart(6, '0')}`; // Örn: Z000001
  }

  async createDailyReport(data: CreateDailyReportDto) {
    // Aynı şube ve tarih için zaten rapor var mı kontrol et
    const existingReport = await this.prisma.dailyReport.findUnique({
      where: {
        branchId_reportDate: {
          branchId: data.branchId,
          reportDate: new Date(data.reportDate).toISOString().split('T')[0] + 'T00:00:00.000Z', // Sadece tarihi karşılaştır
        },
      },
    });
    if (existingReport) {
      throw new ConflictException(`Daily report for branch "${data.branchId}" on ${data.reportDate.toISOString().split('T')[0]} already exists.`);
    }

    // Rapor verilerini hesapla
    const calculatedData = await this.generateReportData(data.branchId, new Date(data.reportDate));

    // Raporu oluştur
    const createData: any = {
        branchId: data.branchId,
        createdBy: data.createdBy,
        reportNo: calculatedData.reportNo,
        reportDate: calculatedData.reportDate,
        totalOrders: calculatedData.totalOrders,
        totalItems: calculatedData.totalItems,
        totalCustomers: calculatedData.totalCustomers,
        averageTicket: calculatedData.averageTicket,
        grossSales: calculatedData.grossSales,
        totalDiscount: calculatedData.totalDiscount,
        totalServiceCharge: calculatedData.totalServiceCharge,
        netSales: calculatedData.netSales,
        totalTax: calculatedData.totalTax,
        totalSales: calculatedData.totalSales,
        cashSales: calculatedData.cashSales,
        creditCardSales: calculatedData.creditCardSales,
        debitCardSales: calculatedData.debitCardSales,
        mealCardSales: calculatedData.mealCardSales,
        otherSales: calculatedData.otherSales,
        totalReturns: calculatedData.totalReturns,
        totalCancellations: calculatedData.totalCancellations,
        openingBalance: calculatedData.openingBalance,
        totalCashIn: calculatedData.totalCashIn,
        totalCashOut: calculatedData.totalCashOut,
        expectedBalance: calculatedData.expectedBalance,
        actualBalance: calculatedData.expectedBalance, // Başlangıçta expected balance ile aynı
        difference: 0, // Başlangıçta 0
        taxBreakdown: calculatedData.taxBreakdown,
        categoryBreakdown: calculatedData.categoryBreakdown,
        hourlyBreakdown: calculatedData.hourlyBreakdown,
    };

    // DTO'dan gelen opsiyonel alanları ekle
    if (data.actualBalance !== undefined) {
        createData.actualBalance = data.actualBalance;
    }
    if (data.difference !== undefined) {
        createData.difference = data.difference;
    }
    if (data.zReportNo) {
        createData.zReportNo = data.zReportNo;
    }
    if (data.fiscalId) {
        createData.fiscalId = data.fiscalId;
    }
    if (data.approvedBy) {
        createData.approvedBy = data.approvedBy;
    }

    return this.prisma.dailyReport.create({
      data: createData,
    });
  }

  async findAllDailyReports(branchId?: string, startDate?: Date, endDate?: Date) {
    return this.prisma.dailyReport.findMany({
      where: {
        branchId: branchId || undefined,
        reportDate: {
          gte: startDate ? new Date(startDate.toISOString().split('T')[0]) : undefined,
          lte: endDate ? new Date(endDate.toISOString().split('T')[0]) : undefined,
        },
      },
      include: {
        branch: { select: { id: true, name: true } },
      },
      orderBy: { reportDate: 'desc' },
    });
  }

  async findOneDailyReport(id: string) {
    const report = await this.prisma.dailyReport.findUnique({
      where: { id },
      include: { branch: { select: { id: true, name: true } } },
    });
    if (!report) {
      throw new NotFoundException(`Daily report with ID "${id}" not found.`);
    }
    return report;
  }

  // DailyReport'lar genellikle çok sınırlı güncellemeler alır (örn: onaylama, zReportNo)
  async updateDailyReport(id: string, data: UpdateDailyReportDto) {
    const existingReport = await this.findOneDailyReport(id);

    // Yalnızca belirli alanların güncellenmesine izin ver (iş kuralı olarak)
    const allowedUpdates = {
        approvedBy: data.approvedBy,
        approvedAt: data.approvedAt,
        zReportNo: data.zReportNo,
        fiscalId: data.fiscalId,
        actualBalance: data.actualBalance,
        difference: data.difference,
        printedAt: data.printedAt,
        emailedAt: data.emailedAt,
    };

    try {
      return await this.prisma.dailyReport.update({
        where: { id },
        data: allowedUpdates, // Sadece izin verilen alanları güncelle
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Daily report with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  // DailyReport'lar genellikle silinmez, finansal kayıtlar kalıcı olmalıdır.
  // Ancak CRUD'un temelini oluşturmak için bu metodu da ekliyoruz.
  // Üretim ortamında bu endpoint'in erişimi çok kısıtlı olmalıdır.
  async removeDailyReport(id: string) {
    try {
      return await this.prisma.dailyReport.delete({
        where: { id },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Daily report with ID "${id}" not found.`);
      }
      throw error;
    }
  }
}
