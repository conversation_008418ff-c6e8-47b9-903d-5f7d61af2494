// src/price-override/dto/create-price-override.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsNumber,
  Min,
  IsDateString,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreatePriceOverrideDto {
  @IsString()
  @IsNotEmpty()
  branchId: string; // Hangi şubeye özel fiyat olduğu

  @IsString()
  @IsNotEmpty()
  productId: string; // Hangi ürüne özel fiyat olduğu

  @IsString()
  @IsOptional()
  variantId?: string; // Hangi varyanta özel fiyat olduğu (opsiyonel)

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsNotEmpty()
  overridePrice: number; // Geçersiz kılınan yeni fiyat

  @IsString()
  @IsOptional()
  reason?: string; // Fiyat değişik<PERSON>ğ<PERSON> neden<PERSON> (örn: "<PERSON>ğ<PERSON>ci indirimi", "Happy Hour")

  @IsDateString()
  @IsNotEmpty()
  startDate: Date;

  @IsOptional()
  @IsDateString()
  endDate?: Date;

  @IsString()
  @IsNotEmpty()
  createdBy: string; // Fiyatı oluşturan kullanıcı ID'si
}
