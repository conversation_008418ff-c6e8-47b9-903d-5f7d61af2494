// src/customer/customer.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { CustomerService } from './customer.service';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';

@Controller('customer')
export class CustomerController {
  constructor(private readonly customerService: CustomerService) {}

  @Post() // POST /customer
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createCustomerDto: CreateCustomerDto) {
    return this.customerService.createCustomer(createCustomerDto);
  }

  @Get() // GET /customer?phone=...&email=...&taxNumber=...
  findAll(
    @Query('phone') phone?: string,
    @Query('email') email?: string,
    @Query('taxNumber') taxNumber?: string,
  ) {
    return this.customerService.findAllCustomers(phone, email, taxNumber);
  }

  @Get(':id') // GET /customer/:id
  findOne(@Param('id') id: string) {
    return this.customerService.findOneCustomer(id);
  }

  @Patch(':id') // PATCH /customer/:id
  update(@Param('id') id: string, @Body() updateCustomerDto: UpdateCustomerDto) {
    return this.customerService.updateCustomer(id, updateCustomerDto);
  }

  @Delete(':id') // DELETE /customer/:id
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.customerService.removeCustomer(id);
  }
}
