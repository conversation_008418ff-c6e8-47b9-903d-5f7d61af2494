// src/stock-count/stock-count.service.ts
import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateStockCountDto, CreateStockCountItemDto } from './dto/create-stock-count.dto';
import { UpdateStockCountDto, UpdateStockCountItemDto } from './dto/update-stock-count.dto';
import { StockCountStatus, StockMovementType } from '../../generated/prisma';
import { StockMovementService } from '../stock-movement/stock-movement.service'; // StockMovementService'i import et

@Injectable()
export class StockCountService {
  constructor(
    private prisma: PrismaService,
    private stockMovementService: StockMovementService, // StockMovementService'i enjekte et
  ) {}

  // Stok sayım kalemlerini hesaplayan ve doğrulayan yardımcı fonksiyon
  private async processStockCountItems(items: CreateStockCountItemDto[], branchId: string) {
    const processedItems: any[] = [];
    for (const itemDto of items) {
      const inventoryItem = await this.prisma.inventoryItem.findUnique({
        where: { id: itemDto.inventoryItemId, deletedAt: null },
      });
      if (!inventoryItem) {
        throw new NotFoundException(`Inventory item with ID "${itemDto.inventoryItemId}" not found for stock count item.`);
      }

      // Sistemdeki mevcut miktar ve ortalama maliyet
      const systemQuantity = inventoryItem.currentStock.toNumber();
      const unitCost = inventoryItem.averageCost?.toNumber() || 0;
      const countedQuantity = parseFloat(itemDto.countedQuantity!.toFixed(3));
      const difference = countedQuantity - systemQuantity;
      const totalDifference = parseFloat((difference * unitCost).toFixed(2));

      processedItems.push({
        inventoryItemId: itemDto.inventoryItemId,
        systemQuantity: parseFloat(systemQuantity.toFixed(3)),
        countedQuantity: countedQuantity,
        difference: parseFloat(difference.toFixed(3)),
        unitCost: parseFloat(unitCost.toFixed(2)),
        totalDifference: totalDifference,
        note: itemDto.note,
      });
    }
    return processedItems;
  }

  async createStockCount(data: CreateStockCountDto) {
    // Branch ve User var mı kontrol et
    const branchExists = await this.prisma.branch.findUnique({ where: { id: data.branchId, deletedAt: null } });
    if (!branchExists) { throw new NotFoundException(`Branch with ID "${data.branchId}" not found.`); }
    const createdByExists = await this.prisma.user.findUnique({ where: { id: data.createdBy, deletedAt: null } });
    if (!createdByExists) { throw new NotFoundException(`User (createdBy) with ID "${data.createdBy}" not found.`); }
    if (data.approvedBy) {
        const approvedByExists = await this.prisma.user.findUnique({ where: { id: data.approvedBy, deletedAt: null } });
        if (!approvedByExists) { throw new NotFoundException(`User (approvedBy) with ID "${data.approvedBy}" not found.`); }
    }
    // CountedBy kullanıcıları da kontrol edilebilir

    // Aynı şube ve tarih için zaten rapor var mı kontrol et
    const existingCount = await this.prisma.stockCount.findFirst({
      where: {
        branchId: data.branchId,
        countDate: {
          gte: new Date(data.countDate).toISOString().split('T')[0] + 'T00:00:00.000Z',
          lt: new Date(data.countDate).toISOString().split('T')[0] + 'T23:59:59.999Z',
        },
      },
    });
    if (existingCount) {
      throw new ConflictException(`Stock count for branch "${data.branchId}" on ${new Date(data.countDate).toISOString().split('T')[0]} already exists.`);
    }

    const processedItems = await this.processStockCountItems(data.items, data.branchId);

    const stockCount = await this.prisma.stockCount.create({
      data: {
        ...data,
        countDate: new Date(data.countDate), // Date objesine dönüştür
        status: data.status || StockCountStatus.DRAFT,
        items: {
          create: processedItems.map(item => ({
            inventoryItemId: item.inventoryItemId,
            systemQuantity: item.systemQuantity,
            countedQuantity: item.countedQuantity,
            difference: item.difference,
            unitCost: item.unitCost,
            totalDifference: item.totalDifference,
            note: item.note,
          })),
        },
      },
      include: { items: true },
    });

    // Eğer sayım APPROVED ise, farklar için StockMovement oluştur
    if (stockCount.status === 'APPROVED') {
        await this.applyStockCountAdjustments(stockCount.id, stockCount.branchId, stockCount.createdBy);
    }

    return stockCount;
  }

  async findAllStockCounts(branchId?: string, countType?: any, status?: StockCountStatus, startDate?: Date, endDate?: Date) {
    return this.prisma.stockCount.findMany({
      where: {
        branchId: branchId || undefined,
        countType: countType || undefined,
        status: status || undefined,
        countDate: {
          gte: startDate ? new Date(startDate.toISOString().split('T')[0]) : undefined,
          lte: endDate ? new Date(endDate.toISOString().split('T')[0] + 'T23:59:59.999Z') : undefined,
        },
      },
      include: {
        // items: true, // Eğer sayım kalemlerini de dahil etmek istersen
        // branch: { select: { id: true, name: true } }, // Branch ilişkisi şemada tanımlı değilse kaldır
      },
      orderBy: { countDate: 'desc' },
    });
  }

  async findOneStockCount(id: string) {
    const stockCount = await this.prisma.stockCount.findUnique({
      where: { id },
      include: {
        // branch: { select: { id: true, name: true } }, // Branch ilişkisi şemada tanımlı değilse kaldır
        items: { include: { inventoryItem: { select: { id: true, name: true, unit: true, currentStock: true, averageCost: true } } } },
      },
    });
    if (!stockCount) {
      throw new NotFoundException(`Stock count with ID "${id}" not found.`);
    }
    return stockCount;
  }

  async updateStockCount(id: string, data: UpdateStockCountDto) {
    const existingStockCount = await this.findOneStockCount(id);

    if (existingStockCount.status === 'APPROVED' || existingStockCount.status === 'CANCELLED') {
        throw new BadRequestException(`Cannot update a stock count with status "${existingStockCount.status}".`);
    }

    // Branch ve User varlık kontrolleri
    if ((data as any).branchId && (data as any).branchId !== existingStockCount.branchId) {
        const branchExists = await this.prisma.branch.findUnique({ where: { id: (data as any).branchId, deletedAt: null } });
        if (!branchExists) throw new NotFoundException(`Branch with ID "${(data as any).branchId}" not found.`);
    }
    if (data.approvedBy && data.approvedBy !== existingStockCount.approvedBy) {
        const approvedByExists = await this.prisma.user.findUnique({ where: { id: data.approvedBy, deletedAt: null } });
        if (!approvedByExists) throw new NotFoundException(`User (approvedBy) with ID "${data.approvedBy}" not found.`);
    }
    // CountedBy kullanıcıları da kontrol edilebilir

    // Aynı şube ve tarih için zaten rapor var mı kontrol et (eğer tarih veya şube değişiyorsa)
    if ((data as any).branchId || (data as any).countDate) {
        const targetBranchId = (data as any).branchId || existingStockCount.branchId;
        const targetCountDate = (data as any).countDate ? new Date((data as any).countDate) : existingStockCount.countDate;

        const existingWithNewData = await this.prisma.stockCount.findFirst({
            where: {
                branchId: targetBranchId,
                countDate: {
                    gte: targetCountDate.toISOString().split('T')[0] + 'T00:00:00.000Z',
                    lt: targetCountDate.toISOString().split('T')[0] + 'T23:59:59.999Z',
                },
            },
        });
        if (existingWithNewData && existingWithNewData.id !== id) {
            throw new ConflictException(`Stock count for branch "${targetBranchId}" on ${targetCountDate.toISOString().split('T')[0]} already exists.`);
        }
    }

    const transaction: any[] = [];
    let updatedItemsData: any[] = [];

    // Stok sayım kalemleri güncellemelerini yönet
    if (data.items) {
        // Mevcut kalemleri (DB'deki) al
        const currentStockCountItems = await this.prisma.stockCountItem.findMany({ where: { stockCountId: id } });
        const currentItemIds = new Set(currentStockCountItems.map(item => item.id));
        const incomingItemIds = new Set(data.items.filter(item => item.id).map(item => item.id));

        // Silinecek kalemleri belirle
        const itemsToDelete = currentStockCountItems.filter(item => !incomingItemIds.has(item.id));
        if (itemsToDelete.length > 0) {
            transaction.push(this.prisma.stockCountItem.deleteMany({
                where: { id: { in: itemsToDelete.map(item => item.id) } }
            }));
        }

        // Güncellenecek ve eklenecek kalemleri işle
        for (const item of data.items) {
            if (item.id && currentItemIds.has(item.id)) { // Güncelleme
                const processedItem = (await this.processStockCountItems([item as CreateStockCountItemDto], existingStockCount.branchId))[0] as any;
                transaction.push(this.prisma.stockCountItem.update({
                    where: { id: item.id },
                    data: {
                        countedQuantity: processedItem.countedQuantity,
                        difference: processedItem.difference,
                        totalDifference: processedItem.totalDifference,
                        note: item.note,
                        // inventoryItemId, systemQuantity, unitCost değişmez
                    }
                }));
                updatedItemsData.push({ ...processedItem, id: item.id }); // Sonradan kullanılmak üzere
            } else { // Yeni ekleme
                const processedItem = (await this.processStockCountItems([item as CreateStockCountItemDto], existingStockCount.branchId))[0] as any;
                transaction.push(this.prisma.stockCountItem.create({
                    data: {
                        stockCountId: id,
                        inventoryItemId: processedItem.inventoryItemId,
                        systemQuantity: processedItem.systemQuantity,
                        countedQuantity: processedItem.countedQuantity,
                        difference: processedItem.difference,
                        unitCost: processedItem.unitCost,
                        totalDifference: processedItem.totalDifference,
                        note: processedItem.note,
                    }
                }));
                updatedItemsData.push(processedItem);
            }
        }
    } else { // items dizisi gönderilmemişse, mevcutları aynen al
        updatedItemsData = (await this.prisma.stockCountItem.findMany({ where: { stockCountId: id } })).map(item => ({
            inventoryItemId: item.inventoryItemId,
            systemQuantity: item.systemQuantity.toNumber(),
            countedQuantity: item.countedQuantity.toNumber(),
            difference: item.difference.toNumber(),
            unitCost: item.unitCost?.toNumber() || 0,
            totalDifference: item.totalDifference?.toNumber() || 0,
            note: item.note,
            id: item.id
        }));
    }

    // Ana sayım bilgilerini güncelle
    const updatedStockCountData: any = {
        ...data,
        items: undefined, // items array'ini doğrudan güncellemeyeceğiz, transaction ile yönettik
        countDate: (data as any).countDate ? new Date((data as any).countDate) : undefined,
        startedAt: data.startedAt || undefined,
        completedAt: data.completedAt || undefined,
        approvedAt: data.approvedAt || undefined,
    };

    transaction.push(this.prisma.stockCount.update({
        where: { id },
        data: updatedStockCountData,
    }));

    try {
        await this.prisma.$transaction(transaction);

        // Eğer statü APPROVED olduysa ve daha önce APPROVED değilse, stok düzeltmeleri yap
        if ((data.status as any) === 'APPROVED' && (existingStockCount.status as any) !== 'APPROVED') {
            await this.applyStockCountAdjustments(id, existingStockCount.branchId, data.approvedBy || existingStockCount.createdBy);
        }

        return this.findOneStockCount(id); // Güncellenmiş sayımı ilişkileriyle birlikte dön
    } catch (error) {
        if ((error as any).code === 'P2025') {
            throw new NotFoundException(`Stock count with ID "${id}" not found.`);
        }
        throw error;
    }
  }

  // APPROVED olan sayım farklarını stok hareketlerine dönüştürür
  private async applyStockCountAdjustments(stockCountId: string, branchId: string, userId: string) {
    const stockCount = await this.prisma.stockCount.findUnique({
        where: { id: stockCountId },
        include: { items: true }
    });

    if (!stockCount) {
        throw new NotFoundException(`Stock count with ID "${stockCountId}" not found for adjustment.`);
    }
    if (stockCount.status !== 'APPROVED') {
        throw new BadRequestException(`Stock count with ID "${stockCountId}" is not in APPROVED status.`);
    }

    for (const item of stockCount.items) {
        if (item.difference.toNumber() !== 0) {
            // Fark varsa ayarlama hareketi oluştur
            const movementType = item.difference.toNumber() > 0 ? StockMovementType.ADJUSTMENT : StockMovementType.ADJUSTMENT; // Hem fazlalık hem eksiklik için ADJUSTMENT
            const quantity = Math.abs(item.difference.toNumber()); // Miktar her zaman pozitif gönderilir, servis tipi ayarlar

            const inventoryItem = await this.prisma.inventoryItem.findUnique({ where: { id: item.inventoryItemId } });

            if (!inventoryItem) {
                throw new NotFoundException(`Inventory item with ID "${item.inventoryItemId}" not found for adjustment.`);
            }

            await this.stockMovementService.createStockMovement({
                branchId: branchId,
                inventoryItemId: item.inventoryItemId,
                type: movementType,
                quantity: quantity,
                unit: inventoryItem.unit, // Envanter öğesinin birimini kullan
                unitCost: item.unitCost?.toNumber() || 0, // Birim maliyeti kullan
                reason: `Stock Count Adjustment (${stockCount.countType} - ${item.difference.toNumber() > 0 ? 'Surplus' : 'Shortage'})`,
                referenceId: stockCount.id,
                referenceType: 'STOCK_COUNT',
                createdBy: userId,
                // Eğer fark negatifse, StockMovementService'te quantity negatif olarak ayarlanacak
                // Eğer fazlalık veya eksiklik tipi farklı ise, ona göre ayarlanabilir.
            });
        }
    }
    console.log(`Stock adjustments applied for Stock Count ID: ${stockCountId}`);
  }


  async removeStockCount(id: string) {
    // Finansal raporlar gibi, stok sayımları da genellikle fiziksel silinmez.
    // Ancak CRUD'un temelini oluşturmak için bu metodu da ekliyoruz.
    // Üretim ortamında bu endpoint'in erişimi çok kısıtlı olmalıdır.
    // Şemada deletedAt alanı yok, bu nedenle fiziksel silme yapıyoruz.
    try {
      const stockCount = await this.prisma.stockCount.delete({
        where: { id },
      });
      return stockCount;
    } catch (error) {
      if ((error as any).code === 'P2025') {
        throw new NotFoundException(`Stock count with ID "${id}" not found.`);
      }
      throw error;
    }
  }
}
