// src/order-log/dto/create-order-log.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsJSON,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateOrderLogDto {
  @IsString()
  @IsNotEmpty()
  orderId: string;

  @IsString()
  @IsOptional() // null if system action (e.g. auto-confirm)
  userId?: string;

  @IsString()
  @IsNotEmpty()
  action: string; // "ORDER_CREATED", "ITEM_ADDED", "STATUS_UPDATED"

  @IsOptional()
  details?: any; // additional context like status change (old, new) (JSON)

  // timestamp alanı servis tarafından otomatik atanacak
}
