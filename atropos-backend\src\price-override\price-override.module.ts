// src/price-override/price-override.module.ts
import { Module } from '@nestjs/common';
import { PriceOverrideService } from './price-override.service';
import { PriceOverrideController } from './price-override.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [PriceOverrideController],
  providers: [PriceOverrideService],
  exports: [PriceOverrideService],
})
export class PriceOverrideModule {}
