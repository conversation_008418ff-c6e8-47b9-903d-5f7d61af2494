// src/stock-movement/dto/create-stock-movement.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsNumber,
  Min,
  IsEnum,
  IsArray,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ProductUnit, StockMovementType } from '../../../generated/prisma'; // Enum'ları import et

export class CreateStockMovementDto {
  @IsString()
  @IsNotEmpty()
  branchId: string;

  @IsString()
  @IsOptional() // Ürün veya envanter öğesi ID'si olmalı, ikisi birden olmamalı
  productId?: string;

  @IsString()
  @IsOptional() // Ürün veya envanter öğesi ID'si olmalı, ikisi birden olmamalı
  inventoryItemId?: string;

  @IsEnum(StockMovementType)
  @IsNotEmpty()
  type: StockMovementType; // PURCHASE, SALE, WASTE, TRANSFER_IN/OUT, ADJUSTMENT vb.

  @IsString()
  @IsOptional()
  reason?: string; // Detaylı açıklama

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 3 })
  @IsNotEmpty()
  quantity: number; // Hareket miktarı (pozitif veya negatif)

  @IsEnum(ProductUnit)
  @IsNotEmpty()
  unit: ProductUnit; // Miktarın birimi

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  unitCost?: number; // Birim maliyet (PURCHASE için)

  // totalCost, previousCost, newAverageCost servis tarafından hesaplanacak

  // previousStock, currentStock servis tarafından hesaplanacak

  @IsString()
  @IsOptional()
  referenceId?: string; // Order ID, Transfer ID, Purchase ID, StockCount ID vb.

  @IsString()
  @IsOptional()
  referenceType?: string; // "ORDER", "TRANSFER", "PURCHASE", "STOCK_COUNT" vb.

  @IsString()
  @IsOptional()
  referenceNo?: string; // Belge no (örn: Fatura No, Transfer No)

  @IsString()
  @IsOptional()
  fromBranchId?: string; // Transfer çıkışı için

  @IsString()
  @IsOptional()
  toBranchId?: string; // Transfer girişi için

  @IsString()
  @IsOptional()
  supplierId?: string; // Tedarikçi ID'si (ileride Supplier modülü eklenince)

  @IsString()
  @IsOptional()
  invoiceNo?: string; // Satın alma faturası no

  @IsString()
  @IsOptional()
  note?: string;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  attachments?: string[]; // Belge resimleri (URL'leri)

  @IsString()
  @IsNotEmpty()
  createdBy: string; // Hareketi yapan kullanıcı ID'si

  @IsString()
  @IsOptional()
  approvedBy?: string; // Onaylayan kullanıcı ID'si
}
